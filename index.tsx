import { GoogleGenAI } from "@google/genai";
import api from "./api";

// AI Service for Gemini API
class GeminiAIService {
    private genAI: GoogleGenAI;

    constructor() {
        // 从环境变量获取API密钥
        const apiKey = process.env.GEMINI_API_KEY || process.env.API_KEY;
        if (!apiKey) {
            throw new Error('Gemini API key not found. Please set GEMINI_API_KEY in environment variables.');
        }

        this.genAI = new GoogleGenAI({ apiKey });
    }

    // 流式生成内容
    async generateContentStream(prompt: string, onChunk: (text: string) => void): Promise<void> {
        try {
            const result = await this.genAI.models.generateContentStream({
                model: "gemini-2.5-flash",
                contents: prompt,
            });

            for await (const chunk of result) {
                const chunkText = chunk.text;
                if (chunkText) {
                    onChunk(chunkText);
                }
            }
        } catch (error) {
            console.error('AI生成内容时出错:', error);
            throw new Error('AI服务暂时不可用，请稍后重试');
        }
    }

    // 创建聊天会话（支持Google搜索）
    createChat(history: Array<{ role: 'user' | 'model', parts: Array<{ text: string }> }> = []) {
        return this.genAI.chats.create({
            model: "gemini-2.5-flash",
            history: history,
            config: {
                maxOutputTokens: 2048,
                temperature: 0.7,
                topP: 0.8,
                topK: 40,
                // 启用Google搜索工具
                tools: [{
                    googleSearch: {}
                }],
            },
        });
    }

    // 发送消息到聊天会话（流式，支持Google搜索）
    async sendMessageStream(chat: any, message: string, onChunk: (text: string) => void, onSearchInfo?: (searchInfo: any) => void): Promise<void> {
        try {
            const result = await chat.sendMessageStream({
                message: message,
            });

            for await (const chunk of result) {
                const chunkText = chunk.text;
                if (chunkText) {
                    onChunk(chunkText);
                }

                // 处理搜索信息（如果有的话）
                if (chunk.groundingMetadata && onSearchInfo) {
                    onSearchInfo(chunk.groundingMetadata);
                }
            }
        } catch (error) {
            console.error('发送消息时出错:', error);
            throw new Error('发送消息失败，请稍后重试');
        }
    }

    // 处理搜索结果并添加引用链接
    addCitationsToText(text: string, groundingMetadata: any): string {
        if (!groundingMetadata || !groundingMetadata.groundingSupports || !groundingMetadata.groundingChunks) {
            return text;
        }

        const supports = groundingMetadata.groundingSupports;
        const chunks = groundingMetadata.groundingChunks;

        // 按结束位置降序排序，避免插入时位置偏移
        const sortedSupports = [...supports].sort((a, b) => (b.segment?.endIndex || 0) - (a.segment?.endIndex || 0));

        let resultText = text;
        for (const support of sortedSupports) {
            const endIndex = support.segment?.endIndex;
            if (endIndex === undefined || !support.groundingChunkIndices?.length) {
                continue;
            }

            const citationLinks = support.groundingChunkIndices
                .map((i: number) => {
                    const uri = chunks[i]?.web?.uri;
                    const title = chunks[i]?.web?.title || `来源${i + 1}`;
                    if (uri) {
                        return `[${i + 1}](${uri} "${title}")`;
                    }
                    return null;
                })
                .filter(Boolean);

            if (citationLinks.length > 0) {
                const citationString = citationLinks.join(', ');
                resultText = resultText.slice(0, endIndex) + citationString + resultText.slice(endIndex);
            }
        }

        return resultText;
    }
}

// Data Interfaces
interface Note {
    id: string;
    title: string;
    content: string;
    createdAt: string;
    size: string;
    folderId: string | null;
}

interface Folder {
    id: string;
    name: string;
    parentId: string | null;
}

interface User {
    username: string;
    isLoggedIn: boolean;
}

// AI Chat Message Interface
interface AIChatMessage {
    id: string;
    role: 'user' | 'assistant';
    content: string;
    timestamp: Date;
    isStreaming?: boolean;
    groundingMetadata?: {
        webSearchQueries?: string[];
        searchEntryPoint?: any;
        groundingChunks?: Array<{
            web?: {
                uri: string;
                title: string;
            };
        }>;
        groundingSupports?: Array<{
            segment?: {
                startIndex: number;
                endIndex: number;
                text: string;
            };
            groundingChunkIndices?: number[];
        }>;
    };
}

// Application State
interface AppState {
    user: User | null;
    folders: Folder[];
    notes: Note[];
    activeFolderId: string | null;
    activeNoteId: string | null;
    isFolderView: boolean;
    editingItemId: string | null; // ID of note/folder being renamed
    sidebarCollapseMode: 'full' | 'partial' | 'minimal'; // 三种侧边栏模式: 完全展开，部分收缩，最小化
    isLoading: boolean; // 加载状态
    errorMessage: string | null; // 错误信息
    controlsVisible: boolean; // 控制面板是否可见
    // AI对话相关状态
    aiChatVisible: boolean; // AI对话面板是否可见
    aiChatMessages: AIChatMessage[]; // AI对话消息历史
    aiIsGenerating: boolean; // AI是否正在生成回复
    aiCurrentResponse: string; // 当前AI正在生成的回复内容
    // 密码管理相关状态
    passwordChangeVisible: boolean; // 密码修改对话框是否可见
    passwordChangeLoading: boolean; // 密码修改是否正在进行
    // 知识库相关状态
    knowledgeBaseVisible: boolean; // 知识库面板是否可见
    knowledgeCurrentView: 'search' | 'analysis' | 'relations' | 'stats'; // 当前视图
    knowledgeSearchResults: any[]; // 搜索结果
    knowledgeSearchLoading: boolean; // 搜索加载状态
    knowledgeSearchQuery: string; // 当前搜索查询
    knowledgeSearchType: 'semantic' | 'keyword' | 'hybrid'; // 搜索类型
    // 分析相关状态
    currentNoteAnalysis: any | null; // 当前笔记的分析结果
    analysisLoading: boolean; // 分析加载状态
    // 关联发现相关状态
    noteRelations: any | null; // 当前笔记的关联信息
    relationsLoading: boolean; // 关联加载状态
    // 统计分析相关状态
    userStats: any | null; // 用户统计信息
    statsLoading: boolean; // 统计加载状态
}

class NotesApp {
    private root: HTMLElement;
    private state: AppState;
    private isCreatingNote = false;
    private isCreatingFolder = false;
    private debounceTimer: number | null = null;
    private aiService: GeminiAIService | null = null;
    private aiChat: any = null; // Gemini chat session
    private scrollTimer: number | null = null;
    private streamingBuffer: string = '';
    private streamingUpdateTimer: number | null = null;

    constructor(rootElement: HTMLElement) {
        if (!rootElement) {
            throw new Error("Root element not found");
        }
        this.root = rootElement;

        // 初始化状态
        this.state = {
            user: null,
            folders: [],
            notes: [],
            activeFolderId: null,
            activeNoteId: null,
            isFolderView: true,
            editingItemId: null,
            sidebarCollapseMode: this.getSavedSidebarMode(),
            isLoading: false,
            errorMessage: null,
            controlsVisible: true, // 默认显示控制面板
            // AI对话相关状态
            aiChatVisible: false,
            aiChatMessages: [],
            aiIsGenerating: false,
            aiCurrentResponse: '',
            // 密码管理相关状态
            passwordChangeVisible: false,
            passwordChangeLoading: false,
            // 知识库相关状态
            knowledgeBaseVisible: false,
            knowledgeCurrentView: 'search',
            knowledgeSearchResults: [],
            knowledgeSearchLoading: false,
            knowledgeSearchQuery: '',
            knowledgeSearchType: 'semantic',
            // 分析相关状态
            currentNoteAnalysis: null,
            analysisLoading: false,
            // 关联发现相关状态
            noteRelations: null,
            relationsLoading: false,
            // 统计分析相关状态
            userStats: null,
            statsLoading: false
        };

        // 检查是否已登录
        this.checkAuthentication();

        // 初始化AI服务
        try {
            this.aiService = new GeminiAIService();
            this.aiChat = this.aiService.createChat();
        } catch (error) {
            console.warn('AI服务初始化失败:', error);
            // AI服务不可用时不影响主要功能
        }

        // 延迟测试字体可用性
        setTimeout(() => {
            this.testFontAvailability();
        }, 2000);
    }

    private async checkAuthentication() {
        try {
            this.setState(() => ({ isLoading: true }));
            // 验证token
            const response = await api.verifyToken();

            // 如果验证成功，设置用户状态并加载数据
            if (response && response.user) {
                this.setState(() => ({
                    user: {
                        username: response.user.username,
                        isLoggedIn: true
                    },
                    isLoading: false
                }));

                // 加载数据
                this.loadData();
            }
        } catch (error) {
            // 验证失败，保持未登录状态
            this.setState(() => ({ isLoading: false }));
        }
    }

    private async loadData() {
        try {
            this.setState(() => ({ isLoading: true }));

            // 加载文件夹
            const folders = await api.getFolders();

            // 将API返回的文件夹数据转换为前端格式
            const formattedFolders: Folder[] = folders.map(folder => ({
                id: folder.id.toString(),
                name: folder.name,
                parentId: folder.parentId ? folder.parentId.toString() : null
            }));

            // 加载笔记
            const notes = await api.getNotes();

            // 将API返回的笔记数据转换为前端格式
            const formattedNotes: Note[] = notes.map(note => ({
                id: note.id.toString(),
                title: note.title,
                content: note.content,
                createdAt: new Date(note.createdAt).toLocaleDateString('zh-CN-u-ca-iso8601'),
                size: note.size,
                folderId: note.folderId ? note.folderId.toString() : null
            }));

            // 更新状态
            this.setState(() => ({
                folders: formattedFolders,
                notes: formattedNotes,
                activeFolderId: formattedFolders.length > 0 ? formattedFolders[0].id : null,
                activeNoteId: formattedNotes.length > 0 ? formattedNotes[0].id : null,
                isLoading: false
            }));
        } catch (error) {
            console.error('加载数据失败:', error);
            this.setState(() => ({
                isLoading: false,
                errorMessage: '加载数据失败，请刷新页面重试'
            }));
        }
    }

    private setState(updater: (prevState: AppState) => Partial<AppState>) {
        const updates = updater(this.state);
        this.state = { ...this.state, ...updates };
        this.render();
    }

    // --- Login Methods ---

    private handleLogin = async (username: string, password: string): Promise<boolean> => {
        try {
            this.setState(() => ({ isLoading: true, errorMessage: null }));

            // 调用API登录
            const response = await api.login(username, password);

            if (response && response.token) {
                // 登录成功，更新状态
                this.setState(() => ({
                    user: {
                        username: response.user.username,
                        isLoggedIn: true
                    },
                    isLoading: false,
                    errorMessage: null
                }));

                // 加载数据
                this.loadData();
                return true;
            }

            // 如果没有token，设置错误信息并重新渲染
            this.setState(() => ({
                isLoading: false,
                errorMessage: '登录失败，请检查用户名和密码'
            }), () => {
                // 状态更新后重新渲染登录表单
                this.render();
            });
            return false;
        } catch (error) {
            console.error('登录错误:', error);
            const errorMessage = error instanceof Error ? error.message : '登录失败';
            this.setState(() => ({
                isLoading: false,
                errorMessage: errorMessage
            }), () => {
                // 状态更新后重新渲染登录表单
                this.render();
            });
            // 重新抛出错误，让调用方能够捕获
            throw error;
        }
    }

    private handleLogout = () => {
        // 清除token
        api.clearToken();

        // 重置状态
        this.setState(() => ({
            user: null,
            folders: [],
            notes: [],
            activeFolderId: null,
            activeNoteId: null,
            isLoading: false,
            errorMessage: null
        }));
    }

    // --- Event Handlers ---

    private handleSelectNavItem = async (id: string | null) => {
        if (id === 'latest') {
            try {
                this.setState(() => ({ isLoading: true }));

                // 获取所有笔记（按最新排序）
                const notes = await api.getNotes();

                // 将API返回的笔记数据转换为前端格式
                const formattedNotes: Note[] = notes.map(note => ({
                    id: note.id.toString(),
                    title: note.title,
                    content: note.content,
                    createdAt: new Date(note.createdAt).toLocaleDateString('zh-CN-u-ca-iso8601'),
                    size: note.size,
                    folderId: note.folderId ? note.folderId.toString() : null
                }));

                // 按创建日期排序
                formattedNotes.sort((a, b) =>
                    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
                );

                this.setState(() => ({
                    activeFolderId: null,
                    isFolderView: false,
                    activeNoteId: formattedNotes[0]?.id || null,
                    editingItemId: null,
                    notes: formattedNotes,
                    isLoading: false
                }));
            } catch (error) {
                console.error('获取最新笔记失败:', error);
                this.setState(() => ({
                    isLoading: false,
                    errorMessage: '获取最新笔记失败'
                }));
            }
        } else {
            // other nav items
        }
    }

    private handleSelectFolder = async (folderId: string) => {
        try {
            this.setState(() => ({ isLoading: true }));

            // 获取文件夹中的笔记
            const notes = await api.getNotes(parseInt(folderId));

            // 将API返回的笔记数据转换为前端格式
            const formattedNotes: Note[] = notes.map(note => ({
                id: note.id.toString(),
                title: note.title,
                content: note.content,
                createdAt: new Date(note.createdAt).toLocaleDateString('zh-CN-u-ca-iso8601'),
                size: note.size,
                folderId: note.folderId ? note.folderId.toString() : null
            }));

            this.setState(() => ({
                activeFolderId: folderId,
                isFolderView: true,
                activeNoteId: formattedNotes[0]?.id || null,
                editingItemId: null,
                notes: formattedNotes,
                isLoading: false
            }));
        } catch (error) {
            console.error('获取文件夹笔记失败:', error);
            this.setState(() => ({
                isLoading: false,
                errorMessage: '获取文件夹笔记失败'
            }));
        }
    };

    private handleSelectNote = async (noteId: string) => {
        if (this.state.editingItemId !== noteId) {
            try {
                this.setState(() => ({ isLoading: true }));

                // 获取笔记详情
                const note = await api.getNote(parseInt(noteId));

                // 将API返回的笔记数据转换为前端格式
                const formattedNote: Note = {
                    id: note.id.toString(),
                    title: note.title,
                    content: note.content,
                    createdAt: new Date(note.createdAt).toLocaleDateString('zh-CN-u-ca-iso8601'),
                    size: note.size,
                    folderId: note.folderId ? note.folderId.toString() : null
                };

                // 更新笔记列表中的笔记
                const updatedNotes = this.state.notes.map(n =>
                    n.id === noteId ? formattedNote : n
                );

                this.setState(() => ({
                    activeNoteId: noteId,
                    editingItemId: null,
                    notes: updatedNotes,
                    isLoading: false
                }));

                // 如果知识库面板打开，根据当前视图加载相应数据
                if (this.state.knowledgeBaseVisible) {
                    if (this.state.knowledgeCurrentView === 'analysis') {
                        this.loadNoteAnalysis(noteId);
                    } else if (this.state.knowledgeCurrentView === 'relations') {
                        this.loadNoteRelations(noteId);
                    }
                }
            } catch (error) {
                console.error('获取笔记详情失败:', error);
                this.setState(() => ({
                    isLoading: false,
                    errorMessage: '获取笔记详情失败',
                    activeNoteId: noteId,
                    editingItemId: null
                }));
            }
        }
    };

    private handleNewNote = async () => {
        if (this.isCreatingNote) return;
        this.isCreatingNote = true;

        try {
            this.setState(() => ({ isLoading: true }));

            // 创建新笔记
            const newNoteData = await api.createNote({
                title: "无标题笔记",
                content: `<div>在此处开始编辑...</div>`,
                folderId: this.state.isFolderView ?
                    (this.state.activeFolderId ? parseInt(this.state.activeFolderId) : undefined) :
                    undefined
            });

            // 转换为前端格式
            const newNote: Note = {
                id: newNoteData.id.toString(),
                title: newNoteData.title,
                content: newNoteData.content,
                createdAt: new Date(newNoteData.createdAt).toLocaleDateString('zh-CN-u-ca-iso8601'),
                size: newNoteData.size,
                folderId: newNoteData.folderId ? newNoteData.folderId.toString() : null,
            };

            this.setState(prev => ({
                notes: [newNote, ...prev.notes],
                activeNoteId: newNote.id,
                editingItemId: newNote.id, // Start renaming in the list immediately
                isLoading: false
            }));
        } catch (error) {
            console.error('创建笔记失败:', error);
            this.setState(() => ({
                isLoading: false,
                errorMessage: '创建笔记失败'
            }));
        } finally {
            setTimeout(() => {
                this.isCreatingNote = false;
            }, 1000);
        }
    };

    private handleNewFolder = async () => {
        if (this.isCreatingFolder) return;
        this.isCreatingFolder = true;

        try {
            this.setState(() => ({ isLoading: true }));

            // 创建新文件夹，parentId=当前文件夹视图中的 activeFolderId，否则 null
            const parentIdNum = this.state.isFolderView && this.state.activeFolderId
                ? parseInt(this.state.activeFolderId)
                : null;
            const newFolderData = await api.createFolder({
                name: "新建文件夹",
                parentId: parentIdNum || undefined
            });

            // 转换为前端格式
            const newFolder: Folder = {
                id: newFolderData.id.toString(),
                name: newFolderData.name,
                parentId: newFolderData.parentId != null ? newFolderData.parentId.toString() : null,
            };

            this.setState(prev => ({
                folders: [...prev.folders, newFolder],
                activeFolderId: newFolder.id,
                isFolderView: true,
                activeNoteId: null,
                editingItemId: newFolder.id, // Start renaming immediately
                isLoading: false
            }));
        } catch (error) {
            console.error('创建文件夹失败:', error);
            this.setState(() => ({
                isLoading: false,
                errorMessage: '创建文件夹失败'
            }));
        } finally {
            setTimeout(() => {
                this.isCreatingFolder = false;
            }, 1000);
        }
    };

    private handleStartRename = (id: string) => {
        this.setState(() => ({ editingItemId: id }));
    };

    private handleFinishRename = async (target: HTMLInputElement) => {
        const id = this.state.editingItemId;
        if (!id) return;

        // 获取当前输入的值，如果为空则不进行更新，保留原名称
        const newName = target.value.trim();
        if (!newName) {
            this.setState(() => ({ editingItemId: null }));
            return;
        }

        try {
            this.setState(() => ({ isLoading: true }));

            // 根据 id 在 notes 或 folders 中查找类型
            const isNote = this.state.notes.some(n => n.id === id);
            if (isNote) {
                const noteId = parseInt(id);
                const note = this.state.notes.find(note => note.id === id);
                if (note) {
                    // 先更新本地状态，确保UI响应更快
                    this.setState(prev => ({
                        notes: prev.notes.map(note => note.id === id ? { ...note, title: newName } : note),
                        editingItemId: null,
                        isLoading: false
                    }));

                    // 然后更新后端
                    await api.updateNote(noteId, { title: newName });
                }
            } else {
                // 更新文件夹名称
                const folderId = parseInt(id);
                const folder = this.state.folders.find(folder => folder.id === id);
                if (folder) {
                    // 先更新本地状态，确保UI响应更快
                    this.setState(prev => ({
                        folders: prev.folders.map(f => f.id === id ? { ...f, name: newName } : f),
                        editingItemId: null,
                        isLoading: false,
                        // 保持当前文件夹为活动状态
                        activeFolderId: prev.activeFolderId === id ? id : prev.activeFolderId,
                        isFolderView: prev.activeFolderId === id ? true : prev.isFolderView
                    }));

                    // 然后更新后端
                    await api.updateFolder(folderId, { name: newName });

                    // 如果重命名的是当前选中的文件夹，重新加载文件夹内容
                    if (this.state.activeFolderId === id) {
                        this.handleSelectFolder(id);
                    }
                }
            }
        } catch (error) {
            console.error('重命名失败:', error);
            this.setState(() => ({
                isLoading: false,
                errorMessage: '重命名失败',
                editingItemId: null
            }));
        }
    };

    private handleTitleChange = async (e: Event) => {
        const target = e.target as HTMLInputElement;
        const newTitle = target.value;
        const activeNoteId = this.state.activeNoteId;

        if (activeNoteId) {
            const note = this.state.notes.find(note => note.id === activeNoteId);
            if (note) {
                note.title = newTitle;
                // 防抖更新
                if (this.debounceTimer) clearTimeout(this.debounceTimer);
                this.debounceTimer = window.setTimeout(async () => {
                    await api.updateNote(parseInt(activeNoteId), { title: newTitle });
                }, 1000);
            }
            const noteListItem = this.root.querySelector(`.note-item[data-note-id="${activeNoteId}"] h3`);
            if (noteListItem) noteListItem.textContent = newTitle;
        }
    };

    private handleContentChange = async (e: Event) => {
        const target = e.target as HTMLElement;
        const activeNoteId = this.state.activeNoteId;
        if (activeNoteId) {
            const activeNote = this.state.notes.find(n => n.id === activeNoteId);
            if (activeNote) {
                const newContent = target.innerHTML;
                activeNote.content = newContent;
                if (this.debounceTimer) clearTimeout(this.debounceTimer);
                this.debounceTimer = window.setTimeout(async () => {
                    await api.updateNote(parseInt(activeNoteId), { content: newContent });
                    // 更新大小…
                }, 1000);
            }
        }
    };

    private handleToolbarAction = (command: string, value: string | null = null) => {
        // 确保编辑器有焦点并保存当前选择
        const editor = this.root.querySelector('.editor-content') as HTMLDivElement;
        if (editor) {
            editor.focus();

            // 执行命令
            document.execCommand(command, false, value || undefined);

            // 触发内容变更事件
            editor.dispatchEvent(new Event('input', { bubbles: true }));
        }
    }

    private handleFontSize = () => {
        // 保存当前选择
        const selection = document.getSelection();
        const range = selection?.getRangeAt(0);

        const sizes = ['1', '2', '3', '4', '5', '6', '7']; // HTML fontSize 使用1-7的值
        const displaySizes = ['8pt', '10pt', '12pt', '14pt', '18pt', '24pt', '36pt']; // 对应显示的大小

        // 创建下拉菜单
        const dropdown = document.createElement('div');
        dropdown.className = 'dropdown-menu font-size-dropdown';
        dropdown.style.position = 'absolute';

        // 添加选项
        sizes.forEach((size, index) => {
            const option = document.createElement('div');
            option.className = 'dropdown-item';
            option.textContent = displaySizes[index];
            option.dataset.value = size;

            option.addEventListener('click', () => {
                // 恢复选择
                if (selection && range) {
                    selection.removeAllRanges();
                    selection.addRange(range);
                }

                // 确保编辑器有焦点
                const editor = this.root.querySelector('.editor-content') as HTMLDivElement;
                if (editor) {
                    editor.focus();
                    document.execCommand('fontSize', false, size);

                    // 更新按钮文本
                    const fontSizeBtn = this.root.querySelector('.font-size-btn');
                    if (fontSizeBtn) {
                        fontSizeBtn.textContent = displaySizes[index].replace('pt', '');
                    }

                    // 触发内容变更事件
                    editor.dispatchEvent(new Event('input', { bubbles: true }));
                }

                // 移除下拉菜单
                document.body.removeChild(dropdown);
            });

            dropdown.appendChild(option);
        });

        // 定位并显示下拉菜单
        const fontSizeBtn = this.root.querySelector('.font-size-btn');
        if (fontSizeBtn) {
            const rect = fontSizeBtn.getBoundingClientRect();
            dropdown.style.top = `${rect.bottom}px`;
            dropdown.style.left = `${rect.left}px`;
            document.body.appendChild(dropdown);

            // 点击其他地方关闭下拉菜单
            const closeDropdown = (e: MouseEvent) => {
                if (!dropdown.contains(e.target as Node)) {
                    // 检查dropdown是否仍然是document.body的子元素
                    if (document.body.contains(dropdown)) {
                        document.body.removeChild(dropdown);
                    }
                    document.removeEventListener('click', closeDropdown);
                }
            };

            // 延迟添加事件监听器，防止立即触发
            setTimeout(() => {
                document.addEventListener('click', closeDropdown);
            }, 0);
        }
    }

    private handleFontChange = (fontName: string) => {
        // 确保编辑器有焦点
        const editor = this.root.querySelector('.editor-content') as HTMLDivElement;
        if (editor) {
            editor.focus();

            // 使用现代方法应用字体样式
            this.applyFontToSelection(fontName);

            // 更新按钮文本
            const fontBtn = this.root.querySelector('#font-dropdown-btn .dropdown-label');
            if (fontBtn) {
                fontBtn.textContent = fontName;
            }

            // 触发内容变更事件
            editor.dispatchEvent(new Event('input', { bubbles: true }));
        }
    }

    private applyFontToSelection = (fontName: string) => {
        const selection = window.getSelection();
        if (!selection || selection.rangeCount === 0) {
            console.log('没有选中内容');
            return;
        }

        const range = selection.getRangeAt(0);
        if (range.collapsed) {
            console.log('选择范围为空');
            return; // 没有选中内容
        }

        const selectedText = range.toString();
        console.log('应用字体:', fontName, '到选中文本:', selectedText);

        // 先尝试使用 execCommand
        try {
            const fontValue = fontName === '默认字体' ? '' : fontName;
            const success = document.execCommand('fontName', false, fontValue);
            console.log('execCommand 结果:', success);

            if (!success) {
                // 如果 execCommand 失败，使用手动方法
                console.log('execCommand 失败，使用手动方法');
                this.wrapSelectionWithFont(range, fontName);
            }
        } catch (error) {
            console.error('execCommand 出错，使用手动方法:', error);
            this.wrapSelectionWithFont(range, fontName);
        }
    }

    private wrapSelectionWithFont = (range: Range, fontName: string) => {
        // 获取选中的内容
        const contents = range.extractContents();

        // 创建包装元素
        const wrapper = document.createElement('span');

        // 设置字体样式
        if (fontName === '默认字体') {
            // 对于默认字体，不设置任何样式
            wrapper.appendChild(contents);
        } else {
            const fontValue = this.getFontFamilyValue(fontName);
            wrapper.style.fontFamily = fontValue;
            wrapper.appendChild(contents);
            console.log('设置字体样式:', fontValue);
            console.log('包装元素HTML:', wrapper.outerHTML);
        }

        // 插入包装元素
        range.insertNode(wrapper);

        // 重新选中内容
        const newRange = document.createRange();
        newRange.selectNodeContents(wrapper);
        const selection = window.getSelection();
        if (selection) {
            selection.removeAllRanges();
            selection.addRange(newRange);
        }

        console.log('字体应用完成');
    }



    private getFontFamilyValue = (fontName: string): string => {
        // 根据字体名称返回合适的CSS font-family值，包含中文字体
        const fontMap: { [key: string]: string } = {
            // 英文字体
            'Arial': 'Arial, sans-serif',
            'Helvetica': 'Helvetica, Arial, sans-serif',
            'Times New Roman': '"Times New Roman", Times, serif',
            'Courier New': '"Courier New", Courier, monospace',
            'Georgia': 'Georgia, serif',
            'Verdana': 'Verdana, sans-serif',
            'Comic Sans MS': '"Comic Sans MS", cursive',
            'Impact': 'Impact, sans-serif',
            'Trebuchet MS': '"Trebuchet MS", sans-serif',
            'Palatino': 'Palatino, serif',

            // macOS 中文字体
            'PingFang SC': '"PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif',
            'PingFang TC': '"PingFang TC", "PingFang SC", "Hiragino Sans GB", sans-serif',
            'Hiragino Sans GB': '"Hiragino Sans GB", "PingFang SC", "Microsoft YaHei", sans-serif',
            'STHeiti': '"STHeiti", "SimHei", "Heiti SC", sans-serif',
            'STSong': '"STSong", "SimSun", "Songti SC", serif',
            'STKaiti': '"STKaiti", "KaiTi", "Kaiti SC", serif',
            'STFangsong': '"STFangsong", "FangSong", serif',
            'Songti SC': '"Songti SC", "STSong", "SimSun", serif',
            'Kaiti SC': '"Kaiti SC", "STKaiti", "KaiTi", serif',
            'Baoli SC': '"Baoli SC", serif',
            'Libian SC': '"Libian SC", serif',
            'Wawati SC': '"Wawati SC", cursive',
            'Weibei SC': '"Weibei SC", serif',
            'Xingkai SC': '"Xingkai SC", cursive',
            'Yuanti SC': '"Yuanti SC", sans-serif',

            // Windows 中文字体
            // '微软雅黑': '"Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", sans-serif',
            // '宋体': '"SimSun", "Songti SC", "STSong", serif',
            // '黑体': '"SimHei", "STHeiti", "Heiti SC", sans-serif',
            // '楷体': '"KaiTi", "Kaiti SC", "STKaiti", serif',
            // '仿宋': '"FangSong", "STFangsong", serif',

            // 华文字体系列
            '华文黑体': '"STXihei", "STHeiti", "SimHei", sans-serif',
            '华文宋体': '"STSong", "SimSun", serif',
            '华文楷体': '"STKaiti", "KaiTi", serif',
            '华文仿宋': '"STFangsong", "FangSong", serif'
        };

        return fontMap[fontName] || `"${fontName}", sans-serif`;
    }

    // 检测系统是否支持某个字体
    private isFontAvailable = (fontName: string): boolean => {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        if (!context) return false;

        // 使用默认字体绘制文本
        context.font = '12px monospace';
        const defaultWidth = context.measureText('测试文字 Test').width;

        // 使用指定字体绘制文本
        context.font = `12px "${fontName}", monospace`;
        const testWidth = context.measureText('测试文字 Test').width;

        // 如果宽度不同，说明字体可用
        return defaultWidth !== testWidth;
    }

    // 测试所有字体的可用性
    private testFontAvailability = () => {
        const chineseFonts = [
            // macOS 中文字体
            'PingFang SC', 'PingFang TC', 'PingFang HK',
            'Hiragino Sans GB', 'Hiragino Sans CNS',
            'STHeiti', 'STSong', 'STKaiti', 'STFangsong',
            'Songti SC', 'Songti TC', 'Kaiti SC', 'Kaiti TC',
            'Baoli SC', 'Libian SC', 'Wawati SC', 'Weibei SC',
            'Xingkai SC', 'Yuanti SC', 'Yuppy SC',
            // Windows 中文字体
            '微软雅黑', '宋体', '黑体', '楷体', '仿宋',
            'Microsoft YaHei', 'SimSun', 'SimHei', 'KaiTi', 'FangSong',
            // 其他常见中文字体
            '华文黑体', '华文宋体', '华文楷体', '华文仿宋',
            'STXihei', 'STSong', 'STKaiti', 'STFangsong'
        ];

        const englishFonts = [
            'Arial', 'Helvetica', 'Times New Roman', 'Courier New', 'Georgia', 'Verdana',
            'Comic Sans MS', 'Impact', 'Trebuchet MS', 'Palatino'
        ];

        console.log('=== 中文字体可用性测试 ===');
        chineseFonts.forEach(font => {
            const available = this.isFontAvailable(font);
            console.log(`${font}: ${available ? '✅ 可用' : '❌ 不可用'}`);
        });

        console.log('\n=== 英文字体可用性测试 ===');
        englishFonts.forEach(font => {
            const available = this.isFontAvailable(font);
            console.log(`${font}: ${available ? '✅ 可用' : '❌ 不可用'}`);
        });
        console.log('=============================');
    }

    private handleFontDropdown = () => {
        // 保存当前选择
        const selection = document.getSelection();
        let range: Range | null = null;

        // 安全地获取选择范围
        if (selection && selection.rangeCount > 0) {
            try {
                range = selection.getRangeAt(0);
                console.log('保存的选择范围:', range.toString());
            } catch (error) {
                console.error('获取选择范围失败:', error);
            }
        } else {
            console.log('没有选中内容');
        }

        // 包含更多中文字体选项
        const fonts = [
            '默认字体',
            // 英文字体
            'Arial',
            'Helvetica',
            'Times New Roman',
            'Courier New',
            'Georgia',
            'Verdana',
            'Comic Sans MS',
            'Impact',
            'Trebuchet MS',
            'Palatino',
            // macOS 中文字体
            'PingFang SC',
            'PingFang TC',
            'Hiragino Sans GB',
            'STHeiti',
            'STSong',
            'STKaiti',
            'STFangsong',
            'Songti SC',
            'Kaiti SC',
            'Baoli SC',
            'Libian SC',
            'Wawati SC',
            'Weibei SC',
            'Xingkai SC',
            'Yuanti SC',
            // Windows 中文字体（作为备选）
            '微软雅黑',
            '宋体',
            '黑体',
            '楷体',
            '仿宋',
            // 华文字体系列
            '华文黑体',
            '华文宋体',
            '华文楷体',
            '华文仿宋'
        ];

        // 创建下拉菜单
        const dropdown = document.createElement('div');
        dropdown.className = 'dropdown-menu font-dropdown';
        dropdown.style.position = 'absolute';

        // 添加选项
        fonts.forEach(font => {
            const option = document.createElement('div');
            option.className = 'dropdown-item';

            // 为每个字体选项设置预览文本和样式
            if (font === '默认字体') {
                option.textContent = font;
                option.style.fontFamily = 'inherit';
            } else {
                // 显示字体名称和预览文字
                option.innerHTML = `<span style="font-family: inherit;">${font}</span> <span style="font-family: ${this.getFontFamilyValue(font)}; margin-left: 8px;">中文字体 ABC</span>`;
                option.style.display = 'flex';
                option.style.alignItems = 'center';
                option.style.justifyContent = 'space-between';
            }
            option.addEventListener('click', () => {
                console.log('点击字体选项:', font);

                // 确保编辑器有焦点
                const editor = this.root.querySelector('.editor-content') as HTMLDivElement;
                if (editor) {
                    editor.focus();

                    // 恢复选择
                    if (selection && range) {
                        try {
                            selection.removeAllRanges();
                            selection.addRange(range);
                            console.log('恢复选择范围成功');
                        } catch (error) {
                            console.error('恢复选择范围失败:', error);
                        }
                    }

                    // 使用改进的字体应用方法
                    this.applyFontToSelection(font);

                    // 更新按钮文本
                    const fontBtn = this.root.querySelector('#font-dropdown-btn .dropdown-label');
                    if (fontBtn) {
                        fontBtn.textContent = font;
                    }

                    // 触发内容变更事件
                    editor.dispatchEvent(new Event('input', { bubbles: true }));
                }

                // 关闭下拉菜单
                if (document.body.contains(dropdown)) {
                    document.body.removeChild(dropdown);
                }
            });
            dropdown.appendChild(option);
        });

        // 定位并显示下拉菜单
        const fontBtn = this.root.querySelector('#font-dropdown-btn');
        if (fontBtn) {
            const rect = fontBtn.getBoundingClientRect();
            dropdown.style.top = `${rect.bottom}px`;
            dropdown.style.left = `${rect.left}px`;
            document.body.appendChild(dropdown);

            // 点击其他地方关闭下拉菜单
            const closeDropdown = (e: MouseEvent) => {
                if (!dropdown.contains(e.target as Node)) {
                    // 检查dropdown是否仍然是document.body的子元素
                    if (document.body.contains(dropdown)) {
                        document.body.removeChild(dropdown);
                    }
                    document.removeEventListener('click', closeDropdown);
                }
            };

            // 延迟添加事件监听器，防止立即触发
            setTimeout(() => {
                document.addEventListener('click', closeDropdown);
            }, 0);
        }
    }

    private handleForeColor = () => {
        // 保存当前选择
        const selection = document.getSelection();
        const range = selection?.getRangeAt(0);

        // 创建颜色选择器容器
        const colorPickerWrapper = document.createElement('div');
        colorPickerWrapper.className = 'color-picker-wrapper';

        // 创建颜色选择器弹出框
        const colorPickerPopup = document.createElement('div');
        colorPickerPopup.className = 'color-picker-popup';

        // 获取预定义颜色
        const colors = (window as any).defaultColors || [
            '#000000', '#434343', '#666666', '#999999', '#b7b7b7', '#cccccc', '#d9d9d9', '#ffffff',
            '#ff0000', '#ff9900', '#ffff00', '#00ff00', '#00ffff', '#0000ff', '#9900ff', '#ff00ff'
        ];

        // 添加颜色选项
        colors.forEach((color: string) => {
            const colorSwatch = document.createElement('div');
            colorSwatch.className = 'color-swatch';
            colorSwatch.style.backgroundColor = color;
            colorSwatch.dataset.color = color;

            colorSwatch.addEventListener('click', () => {
                // 恢复选择
                if (selection && range) {
                    selection.removeAllRanges();
                    selection.addRange(range);
                }

                // 确保编辑器有焦点
                const editor = this.root.querySelector('.editor-content') as HTMLDivElement;
                if (editor) {
                    editor.focus();
                    document.execCommand('foreColor', false, color);

                    // 更新按钮样式
                    const foreColorBtn = this.root.querySelector('.toolbar-btn[data-command="foreColor"]');
                    if (foreColorBtn) {
                        (foreColorBtn as HTMLElement).dataset.value = color;
                    }

                    // 触发内容变更事件
                    editor.dispatchEvent(new Event('input', { bubbles: true }));
                }

                // 移除颜色选择器
                document.body.removeChild(colorPickerWrapper);
            });

            colorPickerPopup.appendChild(colorSwatch);
        });

        // 添加自定义颜色输入
        const customColorInput = document.createElement('div');
        customColorInput.className = 'custom-color-input';

        const colorInput = document.createElement('input');
        colorInput.type = 'color';
        colorInput.value = '#000000';

        const hexInput = document.createElement('input');
        hexInput.type = 'text';
        hexInput.placeholder = '自定义颜色 (HEX)';
        hexInput.value = '#000000';

        colorInput.addEventListener('input', () => {
            hexInput.value = colorInput.value;
        });

        colorInput.addEventListener('change', () => {
            // 恢复选择
            if (selection && range) {
                selection.removeAllRanges();
                selection.addRange(range);
            }

            // 应用颜色
            const editor = this.root.querySelector('.editor-content') as HTMLDivElement;
            if (editor) {
                editor.focus();
                document.execCommand('foreColor', false, colorInput.value);
                editor.dispatchEvent(new Event('input', { bubbles: true }));

                // 更新按钮样式
                const foreColorBtn = this.root.querySelector('.toolbar-btn[data-command="foreColor"]');
                if (foreColorBtn) {
                    (foreColorBtn as HTMLElement).dataset.value = colorInput.value;
                }
            }

            // 移除颜色选择器
            document.body.removeChild(colorPickerWrapper);
        });

        hexInput.addEventListener('input', () => {
            // 验证十六进制颜色格式
            if (/^#[0-9A-F]{6}$/i.test(hexInput.value)) {
                colorInput.value = hexInput.value;
            }
        });

        hexInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                // 恢复选择
                if (selection && range) {
                    selection.removeAllRanges();
                    selection.addRange(range);
                }

                // 应用颜色
                const editor = this.root.querySelector('.editor-content') as HTMLDivElement;
                if (editor) {
                    editor.focus();
                    document.execCommand('foreColor', false, hexInput.value);
                    editor.dispatchEvent(new Event('input', { bubbles: true }));
                }

                // 移除颜色选择器
                document.body.removeChild(colorPickerWrapper);
            }
        });

        customColorInput.appendChild(colorInput);
        customColorInput.appendChild(hexInput);

        colorPickerPopup.appendChild(customColorInput);
        colorPickerWrapper.appendChild(colorPickerPopup);

        // 定位并显示颜色选择器
        const foreColorBtn = this.root.querySelector('.toolbar-btn[data-command="foreColor"]');
        if (foreColorBtn) {
            const rect = foreColorBtn.getBoundingClientRect();
            colorPickerPopup.style.top = '100%';
            colorPickerPopup.style.left = '0';
            colorPickerWrapper.style.position = 'absolute';
            colorPickerWrapper.style.top = `${rect.bottom}px`;
            colorPickerWrapper.style.left = `${rect.left}px`;
            document.body.appendChild(colorPickerWrapper);

            // 点击其他地方关闭颜色选择器
            const closeColorPicker = (e: MouseEvent) => {
                if (!colorPickerWrapper.contains(e.target as Node)) {
                    // 检查是否仍然是document.body的子元素
                    if (document.body.contains(colorPickerWrapper)) {
                        document.body.removeChild(colorPickerWrapper);
                    }
                    document.removeEventListener('click', closeColorPicker);
                }
            };

            // 延迟添加事件监听器，防止立即触发
            setTimeout(() => {
                document.addEventListener('click', closeColorPicker);
            }, 0);
        }
    }

    private handleBackgroundColor = () => {
        // 保存当前选择
        const selection = document.getSelection();
        const range = selection?.getRangeAt(0);

        // 创建颜色选择器容器
        const colorPickerWrapper = document.createElement('div');
        colorPickerWrapper.className = 'color-picker-wrapper';

        // 创建颜色选择器弹出框
        const colorPickerPopup = document.createElement('div');
        colorPickerPopup.className = 'color-picker-popup';

        // 获取预定义颜色
        const colors = (window as any).defaultColors || [
            '#000000', '#434343', '#666666', '#999999', '#b7b7b7', '#cccccc', '#d9d9d9', '#ffffff',
            '#ff0000', '#ff9900', '#ffff00', '#00ff00', '#00ffff', '#0000ff', '#9900ff', '#ff00ff'
        ];

        // 添加颜色选项
        colors.forEach((color: string) => {
            const colorSwatch = document.createElement('div');
            colorSwatch.className = 'color-swatch';
            colorSwatch.style.backgroundColor = color;
            colorSwatch.dataset.color = color;

            colorSwatch.addEventListener('click', () => {
                // 恢复选择
                if (selection && range) {
                    selection.removeAllRanges();
                    selection.addRange(range);
                }

                // 确保编辑器有焦点
                const editor = this.root.querySelector('.editor-content') as HTMLDivElement;
                if (editor) {
                    editor.focus();
                    document.execCommand('hiliteColor', false, color);

                    // 更新按钮样式
                    const hiliteColorBtn = this.root.querySelector('.toolbar-btn[data-command="hiliteColor"]');
                    if (hiliteColorBtn) {
                        (hiliteColorBtn as HTMLElement).dataset.value = color;
                    }

                    // 触发内容变更事件
                    editor.dispatchEvent(new Event('input', { bubbles: true }));
                }

                // 移除颜色选择器
                document.body.removeChild(colorPickerWrapper);
            });

            colorPickerPopup.appendChild(colorSwatch);
        });

        // 添加自定义颜色输入
        const customColorInput = document.createElement('div');
        customColorInput.className = 'custom-color-input';

        const colorInput = document.createElement('input');
        colorInput.type = 'color';
        colorInput.value = '#ffffff';

        const hexInput = document.createElement('input');
        hexInput.type = 'text';
        hexInput.placeholder = '自定义颜色 (HEX)';
        hexInput.value = '#ffffff';

        colorInput.addEventListener('input', () => {
            hexInput.value = colorInput.value;
        });

        colorInput.addEventListener('change', () => {
            // 恢复选择
            if (selection && range) {
                selection.removeAllRanges();
                selection.addRange(range);
            }

            // 应用颜色
            const editor = this.root.querySelector('.editor-content') as HTMLDivElement;
            if (editor) {
                editor.focus();
                document.execCommand('hiliteColor', false, colorInput.value);
                editor.dispatchEvent(new Event('input', { bubbles: true }));

                // 更新按钮样式
                const hiliteColorBtn = this.root.querySelector('.toolbar-btn[data-command="hiliteColor"]');
                if (hiliteColorBtn) {
                    (hiliteColorBtn as HTMLElement).dataset.value = colorInput.value;
                }
            }

            // 移除颜色选择器
            document.body.removeChild(colorPickerWrapper);
        });

        hexInput.addEventListener('input', () => {
            // 验证十六进制颜色格式
            if (/^#[0-9A-F]{6}$/i.test(hexInput.value)) {
                colorInput.value = hexInput.value;
            }
        });

        hexInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                // 恢复选择
                if (selection && range) {
                    selection.removeAllRanges();
                    selection.addRange(range);
                }

                // 应用颜色
                const editor = this.root.querySelector('.editor-content') as HTMLDivElement;
                if (editor) {
                    editor.focus();
                    document.execCommand('hiliteColor', false, hexInput.value);
                    editor.dispatchEvent(new Event('input', { bubbles: true }));
                }

                // 移除颜色选择器
                document.body.removeChild(colorPickerWrapper);
            }
        });

        customColorInput.appendChild(colorInput);
        customColorInput.appendChild(hexInput);

        colorPickerPopup.appendChild(customColorInput);
        colorPickerWrapper.appendChild(colorPickerPopup);

        // 定位并显示颜色选择器
        const hiliteColorBtn = this.root.querySelector('.toolbar-btn[data-command="hiliteColor"]');
        if (hiliteColorBtn) {
            const rect = hiliteColorBtn.getBoundingClientRect();
            colorPickerPopup.style.top = '100%';
            colorPickerPopup.style.left = '0';
            colorPickerWrapper.style.position = 'absolute';
            colorPickerWrapper.style.top = `${rect.bottom}px`;
            colorPickerWrapper.style.left = `${rect.left}px`;
            document.body.appendChild(colorPickerWrapper);

            // 点击其他地方关闭颜色选择器
            const closeColorPicker = (e: MouseEvent) => {
                if (!colorPickerWrapper.contains(e.target as Node)) {
                    // 检查是否仍然是document.body的子元素
                    if (document.body.contains(colorPickerWrapper)) {
                        document.body.removeChild(colorPickerWrapper);
                    }
                    document.removeEventListener('click', closeColorPicker);
                }
            };

            // 延迟添加事件监听器，防止立即触发
            setTimeout(() => {
                document.addEventListener('click', closeColorPicker);
            }, 0);
        }
    }

    private handleInsertImage = () => {
        // 保存当前选择
        const selection = document.getSelection();
        const range = selection?.getRangeAt(0);

        // 创建一个隐藏的文件输入框
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = 'image/*'; // 只接受图片文件
        fileInput.style.display = 'none';
        document.body.appendChild(fileInput);

        // 监听文件选择事件
        fileInput.addEventListener('change', () => {
            if (fileInput.files && fileInput.files[0]) {
                const file = fileInput.files[0];
                const reader = new FileReader();

                reader.onload = (e) => {
                    const imageUrl = e.target?.result as string;

                    // 恢复选择
                    if (selection && range) {
                        selection.removeAllRanges();
                        selection.addRange(range);
                    }

                    // 确保编辑器有焦点
                    const editor = this.root.querySelector('.editor-content') as HTMLDivElement;
                    if (editor) {
                        editor.focus();
                        document.execCommand('insertImage', false, imageUrl);
                        editor.dispatchEvent(new Event('input', { bubbles: true }));
                    }
                };

                // 将文件读取为Data URL
                reader.readAsDataURL(file);
            }

            // 移除文件输入框
            document.body.removeChild(fileInput);
        });

        // 触发文件选择对话框
        fileInput.click();
    }

    private handleInsertTable = () => {
        // 保存当前选择
        const selection = document.getSelection();
        const range = selection?.getRangeAt(0);

        // 默认创建3x3表格
        const rows = 3;
        const cols = 3;

        let tableHtml = '<table border="1" style="width:100%; border-collapse: collapse;">';

        // 创建表头行
        tableHtml += '<thead><tr>';
        for (let i = 0; i < cols; i++) {
            tableHtml += '<th style="border: 1px solid #ddd; padding: 8px; text-align: left;">表头 ' + (i + 1) + '</th>';
        }
        tableHtml += '</tr></thead>';

        // 创建表格内容
        tableHtml += '<tbody>';
        for (let i = 0; i < rows; i++) {
            tableHtml += '<tr>';
            for (let j = 0; j < cols; j++) {
                tableHtml += '<td style="border: 1px solid #ddd; padding: 8px;">单元格 ' + (i + 1) + '-' + (j + 1) + '</td>';
            }
            tableHtml += '</tr>';
        }
        tableHtml += '</tbody></table><br>';

        // 恢复选择
        if (selection && range) {
            selection.removeAllRanges();
            selection.addRange(range);
        }

        // 插入表格到编辑器
        const editor = this.root.querySelector('.editor-content') as HTMLDivElement;
        if (editor) {
            editor.focus();
            document.execCommand('insertHTML', false, tableHtml);
            editor.dispatchEvent(new Event('input', { bubbles: true }));
        }
    }

    private handleInsertCodeBlock = () => {
        // 保存当前选择
        const selection = document.getSelection();
        const range = selection?.getRangeAt(0);

        const codeBlockHtml = '<pre style="background-color: #f5f5f5; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap;"><code>// 在这里输入代码\n</code></pre><br>';

        // 恢复选择
        if (selection && range) {
            selection.removeAllRanges();
            selection.addRange(range);
        }

        // 插入代码块到编辑器
        const editor = this.root.querySelector('.editor-content') as HTMLDivElement;
        if (editor) {
            editor.focus();
            document.execCommand('insertHTML', false, codeBlockHtml);
            editor.dispatchEvent(new Event('input', { bubbles: true }));
        }
    }

    private handleInsertHorizontalRule = () => {
        // 保存当前选择
        const selection = document.getSelection();
        const range = selection?.getRangeAt(0);

        // 恢复选择
        if (selection && range) {
            selection.removeAllRanges();
            selection.addRange(range);
        }

        const editor = this.root.querySelector('.editor-content') as HTMLDivElement;
        if (editor) {
            editor.focus();
            document.execCommand('insertHorizontalRule', false, undefined);
            editor.dispatchEvent(new Event('input', { bubbles: true }));
        }
    }

    private handleInsertTodo = () => {
        // 保存当前选择
        const selection = document.getSelection();
        const range = selection?.getRangeAt(0);

        const todoHtml = '<div><input type="checkbox" style="margin-right: 5px;"><span>待办事项</span></div><br>';

        // 恢复选择
        if (selection && range) {
            selection.removeAllRanges();
            selection.addRange(range);
        }

        // 插入待办事项到编辑器
        const editor = this.root.querySelector('.editor-content') as HTMLDivElement;
        if (editor) {
            editor.focus();
            document.execCommand('insertHTML', false, todoHtml);
            editor.dispatchEvent(new Event('input', { bubbles: true }));
        }
    }

    private handleRunCode = () => {
        const editor = this.root.querySelector('.editor-content') as HTMLDivElement;
        if (!editor) return;

        // 获取选中的文本，如果没有选中则获取所有内容
        const selection = window.getSelection();
        let codeToRun = '';

        if (selection && selection.toString().trim()) {
            codeToRun = selection.toString().trim();
        } else {
            // 尝试从当前光标位置找到代码块
            codeToRun = this.extractCodeFromEditor(editor);
        }

        if (!codeToRun) {
            this.showNotification('没有找到可执行的代码', 'warning');
            return;
        }

        // 检测代码类型并执行
        this.executeCode(codeToRun);
    }

    private extractCodeFromEditor(editor: HTMLDivElement): string {
        // 尝试提取代码块（在<pre>或<code>标签中）
        const codeBlocks = editor.querySelectorAll('pre, code');
        if (codeBlocks.length > 0) {
            return codeBlocks[0].textContent || '';
        }

        // 如果没有代码块，返回所有文本内容
        return editor.textContent || '';
    }

    private executeCode(code: string) {
        // 检测代码类型
        const codeType = this.detectCodeType(code);

        switch (codeType) {
            case 'javascript':
                this.executeJavaScript(code);
                break;
            case 'html':
                this.executeHTML(code);
                break;
            case 'css':
                this.executeCSS(code);
                break;
            default:
                this.showCodeExecutionDialog(code, codeType);
                break;
        }
    }

    private detectCodeType(code: string): string {
        // 简单的代码类型检测
        if (code.includes('function') || code.includes('console.log') || code.includes('var ') || code.includes('let ') || code.includes('const ')) {
            return 'javascript';
        }
        if (code.includes('<html>') || code.includes('<div>') || code.includes('<p>')) {
            return 'html';
        }
        if (code.includes('{') && (code.includes('color:') || code.includes('background:'))) {
            return 'css';
        }
        if (code.includes('public class') || code.includes('System.out.println')) {
            return 'java';
        }
        if (code.includes('def ') || code.includes('print(')) {
            return 'python';
        }
        return 'unknown';
    }

    private executeJavaScript(code: string) {
        try {
            // 创建一个安全的执行环境
            const result = eval(code);
            this.showCodeResult('JavaScript执行结果', result, 'success');
        } catch (error: any) {
            this.showCodeResult('JavaScript执行错误', error.message, 'error');
        }
    }

    private executeHTML(code: string) {
        // 在新窗口中预览HTML
        const newWindow = window.open('', '_blank');
        if (newWindow) {
            newWindow.document.write(code);
            newWindow.document.close();
            this.showNotification('HTML代码已在新窗口中打开', 'success');
        }
    }

    private executeCSS(code: string) {
        // 将CSS应用到当前页面（临时）
        const style = document.createElement('style');
        style.textContent = code;
        document.head.appendChild(style);

        this.showNotification('CSS样式已应用到当前页面', 'success');

        // 5秒后移除样式
        setTimeout(() => {
            document.head.removeChild(style);
            this.showNotification('CSS样式已移除', 'info');
        }, 5000);
    }

    private showCodeExecutionDialog(code: string, codeType: string) {
        const dialog = document.createElement('div');
        dialog.className = 'code-execution-dialog';
        dialog.innerHTML = `
            <div class="dialog-overlay">
                <div class="dialog-content">
                    <div class="dialog-header">
                        <h3>代码执行</h3>
                        <button class="close-btn" onclick="this.closest('.code-execution-dialog').remove()">×</button>
                    </div>
                    <div class="dialog-body">
                        <p>检测到 <strong>${codeType}</strong> 代码，但当前环境不支持直接执行。</p>
                        <pre class="code-preview">${this.escapeHtml(code)}</pre>
                        <p>您可以：</p>
                        <ul>
                            <li>复制代码到相应的开发环境中运行</li>
                            <li>使用在线代码执行工具</li>
                            <li>保存为文件后在本地运行</li>
                        </ul>
                    </div>
                    <div class="dialog-footer">
                        <button onclick="navigator.clipboard.writeText('${code.replace(/'/g, "\\'")}'); this.textContent='已复制!'">复制代码</button>
                        <button onclick="this.closest('.code-execution-dialog').remove()">关闭</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(dialog);
    }

    private showCodeResult(title: string, result: any, type: 'success' | 'error' | 'info') {
        const dialog = document.createElement('div');
        dialog.className = 'code-result-dialog';
        dialog.innerHTML = `
            <div class="dialog-overlay">
                <div class="dialog-content ${type}">
                    <div class="dialog-header">
                        <h3>${title}</h3>
                        <button class="close-btn" onclick="this.closest('.code-result-dialog').remove()">×</button>
                    </div>
                    <div class="dialog-body">
                        <pre class="result-content">${this.escapeHtml(String(result))}</pre>
                    </div>
                    <div class="dialog-footer">
                        <button onclick="this.closest('.code-result-dialog').remove()">关闭</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(dialog);
    }

    private showNotification(message: string, type: 'success' | 'error' | 'warning' | 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;

        // 添加到页面
        document.body.appendChild(notification);

        // 3秒后自动移除
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 3000);
    }

    private handleCreateLink = () => {
        // 获取当前选中的文本
        const editor = this.root.querySelector('.editor-content') as HTMLDivElement;
        if (editor) {
            editor.focus();

            // 保存当前选择
            const selection = document.getSelection();
            const range = selection?.getRangeAt(0);
            const selectedText = selection?.toString() || '';

            // 检查是否在已有链接上
            let existingLink: HTMLAnchorElement | null = null;
            let existingUrl = '';
            let existingTarget = false;

            if (range) {
                const parentElement = range.commonAncestorContainer.parentElement;
                if (parentElement && parentElement.tagName === 'A') {
                    existingLink = parentElement as HTMLAnchorElement;
                    existingUrl = existingLink.href || '';
                    existingTarget = existingLink.target === '_blank';
                }
            }

            // 创建自定义链接对话框
            const dialog = document.createElement('div');
            dialog.className = 'custom-dialog link-dialog';

            // 对话框内容
            dialog.innerHTML = `
                <h3>${existingLink ? '编辑链接' : '插入链接'}</h3>
                <div>
                    <label for="link-text">链接文本:</label>
                    <input type="text" id="link-text" value="${selectedText || (existingLink ? existingLink.textContent || '' : '')}" placeholder="显示的文本">
                </div>
                <div>
                    <label for="link-url">链接地址:</label>
                    <input type="text" id="link-url" value="${existingUrl || 'https://'}" placeholder="https://example.com">
                </div>
                <div class="link-checkbox">
                    <label>
                        <input type="checkbox" id="link-new-tab" ${existingTarget ? 'checked' : ''}> 在新标签页中打开
                    </label>
                </div>
                
                <div class="link-preview">
                    <div class="link-preview-title">链接预览</div>
                    <div id="link-preview-content">
                        <a href="${existingUrl || 'https://'}" target="${existingTarget ? '_blank' : '_self'}">${selectedText || (existingLink ? existingLink.textContent || '链接' : '链接')}</a>
                    </div>
                </div>
                
                <div class="link-dialog-buttons">
                    ${existingLink ? '<button id="remove-link" class="remove-link-btn">移除链接</button>' : ''}
                    <button id="cancel-link">取消</button>
                    <button id="confirm-link">确定</button>
                </div>
            `;

            // 添加遮罩层
            const overlay = document.createElement('div');
            overlay.className = 'dialog-overlay';

            document.body.appendChild(overlay);
            document.body.appendChild(dialog);

            // 获取输入框并聚焦
            const urlInput = dialog.querySelector('#link-url') as HTMLInputElement;
            const textInput = dialog.querySelector('#link-text') as HTMLInputElement;
            const newTabCheckbox = dialog.querySelector('#link-new-tab') as HTMLInputElement;
            const previewContent = dialog.querySelector('#link-preview-content') as HTMLDivElement;

            // 实时更新预览
            const updatePreview = () => {
                const url = urlInput.value;
                const text = textInput.value || '链接';
                const newTab = newTabCheckbox.checked;

                previewContent.innerHTML = `
                    <a href="${url}" ${newTab ? 'target="_blank" rel="noopener noreferrer"' : ''}>${text}</a>
                `;
            };

            // 添加输入事件监听器
            textInput.addEventListener('input', updatePreview);
            urlInput.addEventListener('input', updatePreview);
            newTabCheckbox.addEventListener('change', updatePreview);

            if (urlInput) {
                // 如果没有选中文本，则聚焦到文本输入框
                if (!selectedText && !existingLink && textInput) {
                    textInput.focus();
                } else {
                    urlInput.focus();
                    urlInput.select();
                }
            }

            // 取消按钮事件
            const cancelBtn = dialog.querySelector('#cancel-link');
            if (cancelBtn) {
                cancelBtn.addEventListener('click', () => {
                    document.body.removeChild(dialog);
                    document.body.removeChild(overlay);
                });
            }

            // 移除链接按钮事件
            const removeBtn = dialog.querySelector('#remove-link');
            if (removeBtn && existingLink) {
                removeBtn.addEventListener('click', () => {
                    // 恢复选择
                    if (selection && range) {
                        selection.removeAllRanges();
                        selection.addRange(range);
                    }

                    editor.focus();
                    document.execCommand('unlink', false, undefined);
                    editor.dispatchEvent(new Event('input', { bubbles: true }));

                    document.body.removeChild(dialog);
                    document.body.removeChild(overlay);
                });
            }

            // 确认按钮事件
            const confirmBtn = dialog.querySelector('#confirm-link');
            if (confirmBtn) {
                confirmBtn.addEventListener('click', () => {
                    const url = urlInput?.value || '';
                    const text = textInput?.value || '';
                    const newTab = newTabCheckbox?.checked || false;

                    if (url) {
                        // 恢复选择
                        if (selection && range) {
                            selection.removeAllRanges();
                            selection.addRange(range);
                        }

                        editor.focus();

                        // 如果是编辑现有链接
                        if (existingLink) {
                            // 如果文本被修改
                            if (text !== existingLink.textContent) {
                                existingLink.textContent = text;
                            }

                            // 更新链接地址
                            existingLink.href = url;

                            // 更新打开方式
                            if (newTab) {
                                existingLink.setAttribute('target', '_blank');
                                existingLink.setAttribute('rel', 'noopener noreferrer');
                            } else {
                                existingLink.removeAttribute('target');
                                existingLink.removeAttribute('rel');
                            }
                        } else {
                            // 如果有选中文本，直接应用链接
                            if (selectedText) {
                                document.execCommand('createLink', false, url);

                                // 获取刚刚创建的链接元素
                                const links = editor.querySelectorAll('a');
                                links.forEach(link => {
                                    if (link.href === url || link.href.endsWith(url)) {
                                        // 如果需要在新标签页打开
                                        if (newTab) {
                                            link.setAttribute('target', '_blank');
                                            link.setAttribute('rel', 'noopener noreferrer');
                                        }

                                        // 如果文本被修改
                                        if (text !== selectedText) {
                                            link.textContent = text;
                                        }
                                    }
                                });
                            } else if (text) {
                                // 如果没有选中文本但输入了链接文本，则插入带有文本的链接
                                const attributes = newTab ? ' target="_blank" rel="noopener noreferrer"' : '';
                                const sanitizedUrl = this.escapeHtml(url);
                                const sanitizedText = this.escapeHtml(text);
                                const linkHtml = `<a href="${sanitizedUrl}"${attributes}>${sanitizedText}</a>`;
                                document.execCommand('insertHTML', false, linkHtml);
                            }
                        }

                        editor.dispatchEvent(new Event('input', { bubbles: true }));
                    }

                    document.body.removeChild(dialog);
                    document.body.removeChild(overlay);
                });
            }

            // 按Enter键确认
            const handleKeyDown = (e: KeyboardEvent) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    (confirmBtn as HTMLButtonElement)?.click();
                } else if (e.key === 'Escape') {
                    e.preventDefault();
                    (cancelBtn as HTMLButtonElement)?.click();
                }
            };

            textInput?.addEventListener('keydown', handleKeyDown);
            urlInput?.addEventListener('keydown', handleKeyDown);

            // 更新初始预览
            updatePreview();
        }
    }

    private handleInsertDropdown = () => {
        // 保存当前选择
        const selection = document.getSelection();
        const range = selection?.getRangeAt(0);

        const insertItems = [
            { name: '图片', icon: '🖼️', action: this.handleInsertImage },
            { name: '表格', icon: '📊', action: this.handleInsertTable },
            { name: '链接', icon: '🔗', action: this.handleCreateLink },
            { name: '代码块', icon: '💻', action: this.handleInsertCodeBlock },
            { name: '分隔线', icon: '⎯', action: this.handleInsertHorizontalRule },
            { name: '待办事项', icon: '✓', action: this.handleInsertTodo }
        ];

        // 创建下拉菜单
        const dropdown = document.createElement('div');
        dropdown.className = 'dropdown-menu insert-dropdown';
        dropdown.style.position = 'absolute';

        // 添加选项
        insertItems.forEach(item => {
            const option = document.createElement('div');
            option.className = 'dropdown-item';

            const icon = document.createElement('span');
            icon.className = 'dropdown-item-icon';
            icon.textContent = item.icon;

            const text = document.createElement('span');
            text.className = 'dropdown-item-text';
            text.textContent = item.name;

            option.appendChild(icon);
            option.appendChild(text);

            option.addEventListener('click', () => {
                // 恢复选择
                if (selection && range) {
                    selection.removeAllRanges();
                    selection.addRange(range);
                }

                // 调用对应的处理函数
                if (typeof item.action === 'function') {
                    // 移除下拉菜单
                    document.body.removeChild(dropdown);
                    // 执行操作
                    item.action.call(this);
                }
            });

            dropdown.appendChild(option);
        });

        // 定位并显示下拉菜单
        const insertBtn = this.root.querySelector('#insert-dropdown-btn');
        if (insertBtn) {
            const rect = insertBtn.getBoundingClientRect();
            dropdown.style.top = `${rect.bottom}px`;
            dropdown.style.left = `${rect.left}px`;
            document.body.appendChild(dropdown);

            // 点击其他地方关闭下拉菜单
            const closeDropdown = (e: MouseEvent) => {
                if (!dropdown.contains(e.target as Node)) {
                    // 检查dropdown是否仍然是document.body的子元素
                    if (document.body.contains(dropdown)) {
                        document.body.removeChild(dropdown);
                    }
                    document.removeEventListener('click', closeDropdown);
                }
            };

            // 延迟添加事件监听器，防止立即触发
            setTimeout(() => {
                document.addEventListener('click', closeDropdown);
            }, 0);
        }
    }

    // --- Render Methods ---

    private toggleSidebar = () => {
        this.setState(prev => {
            // Cycle through the modes: full -> partial -> minimal -> full
            let newMode: 'full' | 'partial' | 'minimal';

            if (prev.sidebarCollapseMode === 'full') {
                newMode = 'partial';
            } else if (prev.sidebarCollapseMode === 'partial') {
                newMode = 'minimal';
            } else {
                newMode = 'full';
            }

            // Save to localStorage
            localStorage.setItem('sidebarCollapseMode', newMode);

            return { sidebarCollapseMode: newMode };
        });
    }

    private handleSidebarCollapseModeChange = (mode: 'full' | 'partial' | 'minimal') => {
        // Save to localStorage
        localStorage.setItem('sidebarCollapseMode', mode);
        this.setState(() => ({ sidebarCollapseMode: mode }));
    }

    private renderSidebar(): string {
        // 如果是最小化模式，只渲染控制面板
        if (this.state.sidebarCollapseMode === 'minimal') {
            return `

            `;
        }

        const topLevelFolders = this.state.folders.filter(f => !f.parentId);

        const renderFolderTree = (parentId: string | null) => {
            return this.state.folders
                .filter(f => f.parentId === parentId)
                .map(folder => {
                    const isEditing = this.state.editingItemId === folder.id;
                    const isActive = this.state.activeFolderId === folder.id && this.state.isFolderView;
                    return `
                        <div class="sub-folder-item ${isActive ? 'active' : ''}" data-folder-id="${folder.id}" data-title="${folder.name}">
                            <span class="material-symbols-outlined">folder</span>
                             ${isEditing
                            ? `<input type="text" class="rename-input" value="${folder.name}" data-rename-id="${folder.id}" />`
                            : `<span class="item-name" data-rename-trigger-id="${folder.id}">${folder.name}</span>
                                   <span class="material-symbols-outlined rename-folder-btn" data-folder-id="${folder.id}" title="重命名文件夹">edit</span>
                                   <span class="material-symbols-outlined delete-folder-btn" data-folder-id="${folder.id}" title="删除文件夹">delete</span>`
                        }
                        </div>
                    `
                }).join('');
        };

        return `
            <aside class="sidebar">

                <div class="sidebar-profile">
                    <div class="avatar"><span class="material-symbols-outlined">person</span></div>
                    <span>${this.state.user ? this.state.user.username : '未登录'}</span>
                    ${this.state.user ?
                `<div class="vip-status">💎 VIP</div>
                         <div class="profile-actions">
                             <button id="settings-btn" class="settings-btn" title="设置">
                                 <span class="material-symbols-outlined">settings</span>
                             </button>
                             <button id="logout-btn" class="logout-btn">登出</button>
                         </div>`
                : ''}
                </div>
                <div class="sidebar-actions">
                    <button class="new-note-btn" id="new-note-btn" title="新建笔记">
                        <span class="material-symbols-outlined">add_circle</span>${this.state.sidebarCollapseMode !== 'full' ? '' : '新建'}
                    </button>
                    <button class="new-note-btn" id="new-folder-btn" style="background: #6c757d; margin-top: 8px;" title="新建文件夹">
                        <span class="material-symbols-outlined">create_new_folder</span>${this.state.sidebarCollapseMode !== 'full' ? '' : '新建文件夹'}
                    </button>
                </div>
                <nav class="sidebar-nav">
                    <div class="nav-item ${!this.state.isFolderView ? 'active' : ''}" data-nav-id="latest" data-title="最新">
                        <span class="material-symbols-outlined">update</span> ${this.state.sidebarCollapseMode !== 'full' ? '' : '最新'}
                    </div>
                    ${topLevelFolders.map(folder => {
                    const isEditing = this.state.editingItemId === folder.id;
                    return `
                        <div class="folder-item open ${this.state.activeFolderId?.startsWith(folder.id) ? 'active' : ''}" data-folder-id="${folder.id}" data-title="${folder.name}">
                            <span class="material-symbols-outlined folder-arrow">arrow_drop_down</span>
                            <span class="material-symbols-outlined">folder_open</span>
                            ${isEditing
                            ? `<input type="text" class="rename-input" value="${folder.name}" data-rename-id="${folder.id}" />`
                            : `<span class="item-name" data-rename-trigger-id="${folder.id}">${this.state.sidebarCollapseMode !== 'full' ? '' : folder.name}</span>
                                   <span class="material-symbols-outlined rename-folder-btn" data-folder-id="${folder.id}" title="重命名文件夹">edit</span>
                                   <span class="material-symbols-outlined delete-folder-btn" data-folder-id="${folder.id}" title="删除文件夹">delete</span>`
                        }
                        </div>
                        <div class="sub-folder-list ${this.state.sidebarCollapseMode !== 'full' ? 'hidden' : ''}">
                            ${renderFolderTree(folder.id)}
                        </div>
                        `
                }).join('')}
                    <div class="nav-item" data-title="我的资源"><span class="material-symbols-outlined">hub</span> ${this.state.sidebarCollapseMode !== 'full' ? '' : '我的资源'}</div>
                    <div class="nav-item" data-title="与我分享"><span class="material-symbols-outlined">group</span> ${this.state.sidebarCollapseMode !== 'full' ? '' : '与我分享'}</div>
                    <div class="nav-item" data-title="加星"><span class="material-symbols-outlined">star</span> ${this.state.sidebarCollapseMode !== 'full' ? '' : '加星'}</div>
                    <div class="nav-item" data-title="回收站"><span class="material-symbols-outlined">delete</span> ${this.state.sidebarCollapseMode !== 'full' ? '' : '回收站'}</div>
                    <div class="nav-item" data-title="云协作"><span class="material-symbols-outlined">cloud_sync</span> ${this.state.sidebarCollapseMode !== 'full' ? '' : '云协作'}</div>
                </nav>
                <div class="sidebar-footer">
                    <div class="sidebar-footer-links">
                        <a href="#"><span class="material-symbols-outlined">language</span>官网</a>
                        <a href="#"><span class="material-symbols-outlined">download</span>客户端下载</a>
                    </div>
                </div>
                <div class="sidebar-collapse-control">
                    <button class="collapse-control-btn ${this.state.sidebarCollapseMode === 'full' ? 'active' : ''}" data-mode="full" title="完全展开">
                        <span class="material-symbols-outlined">view_array</span>
                    </button>
                    <button class="collapse-control-btn ${this.state.sidebarCollapseMode === 'partial' ? 'active' : ''}" data-mode="partial" title="部分收缩">
                        <span class="material-symbols-outlined">view_sidebar</span>
                    </button>
                    <button class="collapse-control-btn ${this.state.sidebarCollapseMode === 'minimal' ? 'active' : ''}" data-mode="minimal" title="最小化">
                        <span class="material-symbols-outlined">view_headline</span>
                    </button>
                </div>
            </aside>
        `;
    }

    private renderNoteList(): string {
        const currentNotes = this.state.isFolderView
            ? this.state.notes.filter(n => n.folderId === this.state.activeFolderId)
            : [...this.state.notes].sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

        const currentFolderName = this.state.isFolderView
            ? this.state.folders.find(f => f.id === this.state.activeFolderId)?.name
            : "最新";

        const folderHeader = this.state.isFolderView
            ? `
            <div class="folder-info">
                <button class="back-btn" id="back-to-latest-btn" aria-label="返回">
                    <span class="material-symbols-outlined">arrow_back_ios_new</span>
                </button>
                <h2 class="folder-name-header">${currentFolderName}</h2>
            </div>`
            : `<div class="folder-info"><h2 class="folder-name-header">${currentFolderName || '所有笔记'}</h2></div>`;

        return `
            <section class="note-list-panel">
                <div class="note-list-header">
                    <input type="search" class="search-bar" placeholder="搜索笔记 (⌘+⇧+F)">
                </div>
                ${folderHeader}
                <div class="note-list-count">共 ${currentNotes.length} 项</div>
                <div class="note-list">
                    ${currentNotes.map(note => {
            const isEditing = this.state.editingItemId === note.id;
            return `
                        <div class="note-item ${this.state.activeNoteId === note.id ? 'active' : ''}" data-note-id="${note.id}">
                            ${isEditing
                    ? `<input type="text" class="rename-input" value="${note.title}" data-rename-id="${note.id}" />`
                    : `<h3>${note.title}</h3>
                                   <div class="note-item-actions">
                                     <button class="note-item-more" data-note-id="${note.id}" title="更多操作">⋯</button>
                                     <div class="note-dropdown-menu hidden" id="dropdown-${note.id}">
                                       <div class="dropdown-item" data-action="rename" data-note-id="${note.id}">
                                         <div class="dropdown-item-icon"><span class="material-symbols-outlined">edit</span></div>
                                         <div class="dropdown-item-text">重命名</div>
                                       </div>
                                       <div class="dropdown-item" data-action="duplicate" data-note-id="${note.id}">
                                         <div class="dropdown-item-icon"><span class="material-symbols-outlined">content_copy</span></div>
                                         <div class="dropdown-item-text">复制</div>
                                       </div>
                                       <div class="dropdown-item" data-action="export" data-note-id="${note.id}">
                                         <div class="dropdown-item-icon"><span class="material-symbols-outlined">download</span></div>
                                         <div class="dropdown-item-text">导出</div>
                                       </div>
                                       <div class="dropdown-item" data-action="delete" data-note-id="${note.id}">
                                         <div class="dropdown-item-icon"><span class="material-symbols-outlined">delete</span></div>
                                         <div class="dropdown-item-text">删除</div>
                                       </div>
                                     </div>
                                   </div>`
                }
                            <div class="note-item-snippet">${note.content.replace(/<[^>]*>?/gm, '').substring(0, 40)}</div>
                            <div class="note-item-meta">${note.createdAt} &nbsp;&nbsp; ${note.size}</div>
                        </div>
                    `}).join('')}
                </div>
            </section>
        `;
    }

    private renderEditor(): string {
        const activeNote = this.state.notes.find(n => n.id === this.state.activeNoteId);

        if (!activeNote) {
            return `
                <main class="editor-panel">
                    <div class="empty-state">
                        <span class="material-symbols-outlined">description</span>
                        <h2>选择或创建一篇笔记</h2>
                        <p>在左侧选择一篇笔记开始编辑，或点击"新建"按钮创建新笔记。</p>
                    </div>
                </main>
            `;
        }

        return `
            <main class="editor-panel">
                <div class="editor-header">
                    <div class="editor-header-title">
                        <input class="editor-title-input" type="text" value="${activeNote.title}" placeholder="无标题笔记" />
                    </div>
                    <div class="editor-header-actions">
                        <span class="save-status"><span class="material-symbols-outlined">check_circle</span>已保存</span>
                        <button class="btn icon-btn"><span class="material-symbols-outlined">undo</span></button>
                        <button class="btn icon-btn"><span class="material-symbols-outlined">redo</span></button>
                    </div>
                </div>
                <div class="editor-toolbar">
                    <button class="toolbar-btn" data-command="undo" title="撤销"><span class="material-symbols-outlined">undo</span></button>
                    <button class="toolbar-btn" data-command="redo" title="重做"><span class="material-symbols-outlined">redo</span></button>
                    <div class="toolbar-separator"></div>
                    
                    <div class="toolbar-dropdown">
                        <button class="toolbar-btn dropdown-toggle" id="insert-dropdown-btn">
                            <span class="material-symbols-outlined">add</span>
                            <span class="dropdown-label">插入</span>
                            <span class="material-symbols-outlined dropdown-arrow">arrow_drop_down</span>
                        </button>
                    </div>
                    <div class="toolbar-separator"></div>
                    
                    <div class="toolbar-dropdown">
                        <button class="toolbar-btn dropdown-toggle" id="format-dropdown-btn">
                            <span class="dropdown-label">正文</span>
                            <span class="material-symbols-outlined dropdown-arrow">arrow_drop_down</span>
                        </button>
                    </div>
                    
                    <div class="toolbar-dropdown">
                        <button class="toolbar-btn dropdown-toggle" id="font-dropdown-btn">
                            <span class="dropdown-label">默认字体</span>
                            <span class="material-symbols-outlined dropdown-arrow">arrow_drop_down</span>
                        </button>
                    </div>
                    
                    <button class="toolbar-btn font-size-btn">14</button>
                    <div class="toolbar-separator"></div>
                    
                    <button class="toolbar-btn" data-command="bold"><span class="material-symbols-outlined">format_bold</span></button>
                    <button class="toolbar-btn" data-command="italic"><span class="material-symbols-outlined">format_italic</span></button>
                    <button class="toolbar-btn" data-command="underline"><span class="material-symbols-outlined">format_underlined</span></button>
                    <button class="toolbar-btn" data-command="strikeThrough"><span class="material-symbols-outlined">strikethrough_s</span></button>
                    <div class="toolbar-separator"></div>
                    
                    <button class="toolbar-btn" data-command="foreColor" data-value="#000000"><span class="material-symbols-outlined">format_color_text</span></button>
                    <button class="toolbar-btn" data-command="hiliteColor" data-value="#ffffff"><span class="material-symbols-outlined">format_color_fill</span></button>
                    <div class="toolbar-separator"></div>
                    
                    <button class="toolbar-btn" data-command="insertUnorderedList"><span class="material-symbols-outlined">format_list_bulleted</span></button>
                    <button class="toolbar-btn" data-command="insertOrderedList"><span class="material-symbols-outlined">format_list_numbered</span></button>
                    <button class="toolbar-btn" id="add-todo-btn"><span class="material-symbols-outlined">check_box</span></button>
                    <div class="toolbar-separator"></div>
                    <button class="toolbar-btn" id="run-code-btn" title="运行代码"><span class="material-symbols-outlined">play_arrow</span></button>
                    <div class="toolbar-separator"></div>
                </div>
                <div class="editor-content-wrapper">
                    <div class="editor-content" contenteditable="true">
                        ${activeNote.content}
                    </div>

                    <!-- AI悬浮按钮 -->
                    <button class="ai-float-btn" id="ai-float-btn" title="AI助手">
                        <span class="material-symbols-outlined">smart_toy</span>
                    </button>

                    <!-- 知识库悬浮按钮 -->
                    <button class="ai-float-btn knowledge-float-btn" onclick="app.showKnowledgeBase()" title="智能知识库" style="right: 80px;">
                        <span class="material-symbols-outlined">psychology</span>
                    </button>

                    <!-- AI对话面板 -->
                    <div class="ai-chat-panel ${this.state.aiChatVisible ? 'visible' : ''}" id="ai-chat-panel">
                        <div class="ai-chat-header">
                            <h3><span class="material-symbols-outlined">smart_toy</span>AI助手</h3>
                            <div class="ai-chat-controls">
                                <button class="ai-btn ai-clear-btn" id="ai-clear-btn" title="清空对话">
                                    <span class="material-symbols-outlined">delete</span>
                                </button>
                                <button class="ai-btn ai-close-btn" id="ai-close-btn" title="关闭">
                                    <span class="material-symbols-outlined">close</span>
                                </button>
                            </div>
                        </div>
                        <div class="ai-chat-messages" id="ai-chat-messages">
                            ${this.renderAIChatMessages()}
                        </div>
                        <div class="ai-chat-input-area">
                            ${this.state.aiIsGenerating ? `
                                <div class="ai-generating-indicator">
                                    <div class="generating-dots">
                                        <span></span>
                                        <span></span>
                                        <span></span>
                                    </div>
                                    <span class="generating-text">AI正在思考中...</span>
                                    <button class="stop-generating-btn" id="stop-generating-btn" title="停止生成">
                                        <span class="material-symbols-outlined">stop</span>
                                    </button>
                                </div>
                            ` : ''}
                            <div class="ai-input-wrapper">
                                <textarea class="ai-input" id="ai-input" placeholder="向AI助手提问..." rows="2" ${this.state.aiIsGenerating ? 'disabled' : ''}></textarea>
                                <button class="ai-send-btn" id="ai-send-btn" ${this.state.aiIsGenerating ? 'disabled' : ''}>
                                    <span class="material-symbols-outlined">${this.state.aiIsGenerating ? 'hourglass_empty' : 'send'}</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        `;
    }

    private handleEditorClick = (e: Event) => {
        const mouseEvent = e as MouseEvent;
        const target = e.target as HTMLElement;

        // 隐藏选择工具栏
        this.hideSelectionToolbar();

        // 处理链接点击
        if (target.tagName === 'A' || target.closest('a')) {
            const link = target.tagName === 'A' ? target : target.closest('a');
            const href = link!.getAttribute('href') || '';

            // 如果按下了Ctrl/Command键，则在新标签页打开链接
            if (mouseEvent.ctrlKey || mouseEvent.metaKey) {
                mouseEvent.preventDefault();
                window.open(href, '_blank');
                return;
            }

            // 如果链接设置了target="_blank"，则在新标签页打开
            if (link!.getAttribute('target') === '_blank') {
                mouseEvent.preventDefault();
                window.open(href, '_blank');
                return;
            }

            // 如果点击了链接但没有按Ctrl/Command键，则打开编辑链接对话框
            mouseEvent.preventDefault();
            this.handleCreateLink();
        }
    }

    // 处理编辑器右键菜单
    private handleEditorContextMenu = (e: Event) => {
        const mouseEvent = e as MouseEvent;
        const selection = window.getSelection();
        const selectedText = selection?.toString().trim();

        if (selectedText) {
            mouseEvent.preventDefault();
            this.showContextMenu(mouseEvent.clientX, mouseEvent.clientY, selectedText);
        }
    }

    // 处理编辑器鼠标抬起事件（用于显示选择工具栏）
    private handleEditorMouseUp = (e: Event) => {
        const mouseEvent = e as MouseEvent;

        // 延迟检查选择，确保选择已经完成
        setTimeout(() => {
            const selection = window.getSelection();
            const selectedText = selection?.toString().trim();

            if (selectedText && selectedText.length > 0) {
                this.showSelectionToolbar(mouseEvent.clientX, mouseEvent.clientY, selectedText);
            } else {
                this.hideSelectionToolbar();
            }
        }, 10);
    }

    private attachEventListeners() {
        // --- Event Delegation for main actions ---
        this.root.addEventListener('click', this.handleGlobalClick);

        // --- Event Delegation for Renaming ---
        this.root.addEventListener('dblclick', this.handleGlobalDblClick);
        this.root.addEventListener('focusout', this.handleGlobalFocusOut);
        this.root.addEventListener('keydown', this.handleGlobalKeyDown);

        // --- Scroll event to hide dropdowns ---
        const noteList = this.root.querySelector('.note-list');
        if (noteList) {
            noteList.addEventListener('scroll', () => {
                document.querySelectorAll('.note-dropdown-menu').forEach(menu => {
                    menu.classList.add('hidden');
                });
            });
        }

        // --- Window resize event to hide dropdowns ---
        window.addEventListener('resize', () => {
            document.querySelectorAll('.note-dropdown-menu').forEach(menu => {
                menu.classList.add('hidden');
            });
        });

        // --- Editor Content & Title ---
        const editorPanel = this.root.querySelector('.editor-panel');
        if (editorPanel) {
            editorPanel.addEventListener('input', this.handleEditorInput);

            // 添加编辑器点击事件监听器
            const editorContent = this.root.querySelector('.editor-content');
            if (editorContent) {
                editorContent.addEventListener('click', this.handleEditorClick);
                editorContent.addEventListener('contextmenu', this.handleEditorContextMenu);
                editorContent.addEventListener('mouseup', this.handleEditorMouseUp);
            }
        }
    }

    private detachEventListeners() {
        // Remove all event listeners before re-rendering
        this.root.removeEventListener('click', this.handleGlobalClick);
        this.root.removeEventListener('dblclick', this.handleGlobalDblClick);
        this.root.removeEventListener('focusout', this.handleGlobalFocusOut);
        this.root.removeEventListener('keydown', this.handleGlobalKeyDown);

        const editorPanel = this.root.querySelector('.editor-panel');
        if (editorPanel) {
            editorPanel.removeEventListener('input', this.handleEditorInput);

            // 移除编辑器点击事件监听器
            const editorContent = this.root.querySelector('.editor-content');
            if (editorContent) {
                editorContent.removeEventListener('click', this.handleEditorClick);
            }
        }
    }

    // Event handler methods extracted as class properties to maintain "this" context
    private handleGlobalClick = (e: MouseEvent) => {
        const target = e.target as HTMLElement;

        // 调试：记录所有点击
        console.log('全局点击事件:', target, target.className, target.textContent);

        // 检查是否点击了三个点按钮或其相关元素
        if (target.textContent === '⋯' || target.classList.contains('note-item-more') || target.closest('.note-item-more')) {
            console.log('检测到三个点按钮相关点击！', target);
        }

        // 处理关闭错误信息按钮点击
        if (target.closest('.close-error-btn')) {
            this.setState(() => ({ errorMessage: null }));
            return;
        }

        // 处理登录按钮点击
        if (target.closest('#login-btn')) {
            this.handleLoginSubmit();
            return;
        }

        // 处理登出按钮点击
        if (target.closest('#logout-btn')) {
            this.handleLogout();
            return;
        }

        // 处理设置按钮点击
        if (target.closest('#settings-btn')) {
            this.showPasswordChangeDialog();
            return;
        }

        // --- 密码管理相关事件处理 ---

        // 密码修改对话框关闭按钮
        if (target.closest('#password-change-close') || target.closest('#password-change-cancel')) {
            this.hidePasswordChangeDialog();
            return;
        }

        // 密码修改提交按钮
        if (target.closest('#password-change-submit')) {
            this.handlePasswordChange();
            return;
        }

        // 点击密码修改对话框外部关闭
        if (target.closest('#password-change-overlay') && !target.closest('.modal-dialog')) {
            this.hidePasswordChangeDialog();
            return;
        }

        // Button clicks
        if (target.closest('#new-note-btn')) this.handleNewNote();
        if (target.closest('#new-folder-btn')) this.handleNewFolder();
        if (target.closest('#add-todo-btn')) this.handleInsertTodo();
        if (target.closest('#run-code-btn')) this.handleRunCode();
        if (target.closest('#back-to-latest-btn')) this.handleSelectNavItem('latest');

        // 处理侧边栏切换按钮点击
        if (target.closest('#sidebar-toggle') || target.closest('#sidebar-minimal-toggle') || target.closest('#expand-button')) {
            this.toggleSidebar();
            return;
        }

        // 处理控制面板切换按钮点击
        if (target.closest('#toggle-controls-button')) {
            this.toggleControlsVisibility();
            return;
        }

        // --- AI对话相关事件处理 ---

        // AI悬浮按钮点击
        if (target.closest('#ai-float-btn')) {
            this.toggleAIChat();
            return;
        }

        // AI对话面板关闭按钮
        if (target.closest('#ai-close-btn')) {
            this.setState(() => ({ aiChatVisible: false }));
            return;
        }

        // AI对话清空按钮
        if (target.closest('#ai-clear-btn')) {
            this.clearAIChat();
            return;
        }

        // AI发送按钮
        if (target.closest('#ai-send-btn')) {
            const input = this.root.querySelector('#ai-input') as HTMLTextAreaElement;
            if (input && input.value.trim() && !this.state.aiIsGenerating) {
                const message = input.value.trim();
                input.value = '';
                this.sendMessageToAI(message);
            }
            return;
        }

        // 停止生成按钮
        if (target.closest('#stop-generating-btn')) {
            this.stopAIGeneration();
            return;
        }

        // AI消息操作按钮
        const messageActionBtn = target.closest('.message-action-btn');
        if (messageActionBtn) {
            const action = messageActionBtn.getAttribute('data-action');
            const content = messageActionBtn.getAttribute('data-content');

            if (action === 'insert' && content) {
                this.insertAIContentToEditor(content);
            } else if (action === 'copy' && content) {
                navigator.clipboard.writeText(content).then(() => {
                    // 可以添加复制成功的提示
                    console.log('内容已复制到剪贴板');
                }).catch(err => {
                    console.error('复制失败:', err);
                });
            }
            return;
        }

        // 处理侧边栏收缩控制按钮
        const collapseBtn = target.closest('.collapse-control-btn');
        if (collapseBtn) {
            const mode = collapseBtn.getAttribute('data-mode') as 'full' | 'partial' | 'minimal';
            if (mode) {
                this.handleSidebarCollapseModeChange(mode);
            }
            return;
        }

        // Toolbar
        const toolbarBtn = target.closest<HTMLElement>('.toolbar-btn');
        if (toolbarBtn) {
            if (toolbarBtn.id === 'format-dropdown-btn') {
                this.handleFormatDropdown();
            } else if (toolbarBtn.id === 'font-dropdown-btn') {
                this.handleFontDropdown();
            } else if (toolbarBtn.id === 'insert-dropdown-btn') {
                this.handleInsertDropdown();
            } else if (toolbarBtn.classList.contains('font-size-btn')) {
                this.handleFontSize();
            } else if (toolbarBtn.dataset.command === 'foreColor') {
                this.handleForeColor();
            } else if (toolbarBtn.dataset.command === 'hiliteColor') {
                this.handleBackgroundColor();
            } else if (toolbarBtn.dataset.command === 'createLink') {
                this.handleCreateLink();
            } else if (toolbarBtn.dataset.command === 'insertImage') {
                this.handleInsertImage();
            } else if (toolbarBtn.dataset.command === 'insertTable') {
                this.handleInsertTable();
            } else if (toolbarBtn.dataset.command) {
                this.handleToolbarAction(toolbarBtn.dataset.command, toolbarBtn.dataset.value || null);
            }
        }

        // Nav Items
        const navItem = target.closest<HTMLElement>('.nav-item');
        if (navItem?.dataset.navId) this.handleSelectNavItem(navItem.dataset.navId);

        // Folder Selection
        const folderItem = target.closest<HTMLElement>('.folder-item, .sub-folder-item');
        if (folderItem?.dataset.folderId && !target.closest('.rename-folder-btn') && !target.closest('.delete-folder-btn')) {
            this.handleSelectFolder(folderItem.dataset.folderId);
        }

        // Note Selection
        const noteItem = target.closest<HTMLElement>('.note-item');
        if (noteItem?.dataset.noteId &&
            !target.closest('.note-item-more') &&
            !target.closest('.note-dropdown-menu') &&
            !target.closest('.dropdown-item')) {
            this.handleSelectNote(noteItem.dataset.noteId);
        }

        // 删除文件夹按钮
        if (target.closest('.delete-folder-btn')) {
            const folderId = target.closest<HTMLElement>('.delete-folder-btn')!.dataset.folderId;
            if (folderId) this.handleDeleteFolder(folderId);
            return;
        }

        // 重命名文件夹按钮
        if (target.closest('.rename-folder-btn')) {
            const folderId = target.closest<HTMLElement>('.rename-folder-btn')!.dataset.folderId;
            if (folderId) this.handleStartRename(folderId);
            return;
        }

        // 重命名笔记按钮
        if (target.closest('.rename-note-btn')) {
            const noteId = target.closest<HTMLElement>('.rename-note-btn')!.dataset.noteId;
            if (noteId) this.handleStartRename(noteId);
            return;
        }

        // 删除笔记按钮
        if (target.closest('.delete-note-btn')) {
            const noteId = target.closest<HTMLElement>('.delete-note-btn')!.dataset.noteId;
            if (noteId) this.handleDeleteNote(noteId);
            return;
        }

        // --- 处理下拉菜单的点击 ---
        if (target.closest('.note-item-more')) {
            console.log('点击了更多按钮！');
            e.stopPropagation();
            const moreButton = target.closest('.note-item-more') as HTMLElement;
            const noteId = moreButton.getAttribute('data-note-id')!;
            const dropdown = document.getElementById(`dropdown-${noteId}`) as HTMLElement;
            console.log('noteId:', noteId, 'dropdown:', dropdown);

            // 隐藏所有其他下拉菜单
            document.querySelectorAll('.note-dropdown-menu').forEach(menu => {
                if (menu.id !== `dropdown-${noteId}`) {
                    menu.classList.add('hidden');
                }
            });

            if (dropdown) {
                const isHidden = dropdown.classList.contains('hidden');
                console.log('下拉菜单状态:', isHidden ? '隐藏' : '显示', dropdown.classList.toString());

                if (isHidden) {
                    // 简化位置计算 - 显示在按钮右下方
                    const rect = moreButton.getBoundingClientRect();

                    // 简单定位：按钮右下方
                    const left = rect.left;
                    const top = rect.bottom + 5;

                    console.log('按钮位置:', rect, '菜单位置:', left, top);

                    // 应用位置
                    dropdown.style.left = `${left}px`;
                    dropdown.style.top = `${top}px`;

                    dropdown.classList.remove('hidden');
                    console.log('显示下拉菜单，移除hidden类后:', dropdown.classList.toString());
                } else {
                    dropdown.classList.add('hidden');
                    console.log('隐藏下拉菜单，添加hidden类后:', dropdown.classList.toString());
                }
            }
            return;
        }

        // 如果点击的不是下拉菜单内部，则隐藏所有下拉菜单
        if (!target.closest('.note-dropdown-menu')) {
            document.querySelectorAll('.note-dropdown-menu').forEach(menu => {
                menu.classList.add('hidden');
            });
        }

        // 处理下拉菜单项的点击
        if (target.closest('.dropdown-item')) {
            e.stopPropagation();
            const item = target.closest('.dropdown-item')!;
            const action = item.getAttribute('data-action');
            const noteId = item.getAttribute('data-note-id');

            if (action && noteId) {
                // 隐藏下拉菜单
                document.getElementById(`dropdown-${noteId}`)?.classList.add('hidden');

                // 执行相应的操作
                switch (action) {
                    case 'rename':
                        this.handleStartRename(noteId);
                        break;
                    case 'duplicate':
                        this.handleDuplicateNote(noteId);
                        break;
                    case 'export':
                        this.handleExportNote(noteId);
                        break;
                    case 'delete':
                        this.handleDeleteNote(noteId);
                        break;
                }
            }
        }
    }

    private handleGlobalDblClick = (e: MouseEvent) => {
        const target = e.target as HTMLElement;
        const renameId = target.dataset.renameTriggerId;
        if (renameId) {
            this.handleStartRename(renameId);
        }
    }

    private handleGlobalFocusOut = (e: FocusEvent) => {
        const target = e.target as HTMLInputElement;
        if (target.matches('.rename-input')) {
            // Use setTimeout to allow click events to process first
            // This prevents the focusout from firing before a click on another element
            setTimeout(() => {
                // Only finish rename if we're still editing this item
                if (target.dataset.renameId === this.state.editingItemId) {
                    this.handleFinishRename(target);
                }
            }, 100);
        }
    }

    private handleGlobalKeyDown = (e: KeyboardEvent) => {
        const target = e.target as HTMLInputElement | HTMLTextAreaElement;

        // 处理重命名输入框
        if (target.matches('.rename-input')) {
            if (e.key === 'Enter') {
                e.preventDefault(); // 防止表单提交
                // 立即获取输入值并保存
                const newName = (target as HTMLInputElement).value.trim();
                if (newName) {
                    this.handleFinishRename(target as HTMLInputElement);
                }
            } else if (e.key === 'Escape') {
                this.setState(() => ({ editingItemId: null }));
            }
        }

        // 处理AI输入框
        if (target.matches('.ai-input')) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                const message = (target as HTMLTextAreaElement).value.trim();
                if (message && !this.state.aiIsGenerating) {
                    (target as HTMLTextAreaElement).value = '';
                    this.sendMessageToAI(message);
                }
            } else if (e.key === 'Escape') {
                this.setState(() => ({ aiChatVisible: false }));
            }
        }

        // 处理密码修改表单的键盘事件
        if (target.matches('#current-password, #new-password, #confirm-password')) {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.handlePasswordChange();
            } else if (e.key === 'Escape') {
                this.hidePasswordChangeDialog();
            }
        }

        // Add keyboard shortcut for sidebar toggle (Ctrl+B or Cmd+B)
        if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
            e.preventDefault();
            this.toggleSidebar();
        }

        // Add keyboard shortcut for AI chat toggle (Ctrl+K or Cmd+K)
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            this.toggleAIChat();
        }

        // 快捷键：选中文本后按 Ctrl+Shift+A 或 Cmd+Shift+A 询问AI
        if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'A') {
            e.preventDefault();
            const selection = window.getSelection();
            const selectedText = selection?.toString().trim();

            if (selectedText) {
                this.handleSelectionAction('ask-ai', selectedText);
            } else {
                // 如果没有选中文本，直接打开AI对话
                this.toggleAIChat();
            }
        }

        // 处理登录表单的回车键提交
        if (target.matches('#login-username, #login-password') && e.key === 'Enter') {
            const loginBtn = this.root.querySelector('#login-btn');
            if (loginBtn) {
                (loginBtn as HTMLButtonElement).click();
            }
        }
    }

    private handleEditorInput = (e: Event) => {
        const target = e.target as HTMLElement;
        if (target.matches('.editor-content')) {
            this.handleContentChange(e);
        } else if (target.matches('.editor-title-input')) {
            this.handleTitleChange(e);
        }
    }

    private handleFormatDropdown = () => {
        // 保存当前选择
        const selection = document.getSelection();
        const range = selection?.getRangeAt(0);

        const formats = [
            { name: '正文', command: 'formatBlock', value: 'div' },
            { name: '标题1', command: 'formatBlock', value: 'h1' },
            { name: '标题2', command: 'formatBlock', value: 'h2' },
            { name: '标题3', command: 'formatBlock', value: 'h3' },
            { name: '标题4', command: 'formatBlock', value: 'h4' },
            { name: '标题5', command: 'formatBlock', value: 'h5' },
            { name: '标题6', command: 'formatBlock', value: 'h6' },
            { name: '引用', command: 'formatBlock', value: 'blockquote' },
            { name: '代码', command: 'formatBlock', value: 'pre' }
        ];

        // 创建下拉菜单
        const dropdown = document.createElement('div');
        dropdown.className = 'dropdown-menu format-dropdown';
        dropdown.style.position = 'absolute';

        // 添加选项
        formats.forEach(format => {
            const option = document.createElement('div');
            option.className = 'dropdown-item';
            option.textContent = format.name;
            option.addEventListener('click', () => {
                // 恢复选择
                if (selection && range) {
                    selection.removeAllRanges();
                    selection.addRange(range);
                }

                // 确保编辑器有焦点
                const editor = this.root.querySelector('.editor-content') as HTMLDivElement;
                if (editor) {
                    editor.focus();

                    // 执行格式化命令
                    document.execCommand(format.command, false, format.value);

                    // 更新按钮文本
                    const formatBtn = this.root.querySelector('#format-dropdown-btn .dropdown-label');
                    if (formatBtn) {
                        formatBtn.textContent = format.name;
                    }

                    // 触发内容变更事件
                    editor.dispatchEvent(new Event('input', { bubbles: true }));
                }

                document.body.removeChild(dropdown);
            });
            dropdown.appendChild(option);
        });

        // 定位并显示下拉菜单
        const formatBtn = this.root.querySelector('#format-dropdown-btn');
        if (formatBtn) {
            const rect = formatBtn.getBoundingClientRect();
            dropdown.style.top = `${rect.bottom}px`;
            dropdown.style.left = `${rect.left}px`;
            document.body.appendChild(dropdown);

            // 点击其他地方关闭下拉菜单
            const closeDropdown = (e: MouseEvent) => {
                if (!dropdown.contains(e.target as Node)) {
                    // 检查dropdown是否仍然是document.body的子元素
                    if (document.body.contains(dropdown)) {
                        document.body.removeChild(dropdown);
                    }
                    document.removeEventListener('click', closeDropdown);
                }
            };

            // 延迟添加事件监听器，防止立即触发
            setTimeout(() => {
                document.addEventListener('click', closeDropdown);
            }, 0);
        }
    }

    // 处理登录表单提交
    private handleLoginSubmit = async () => {
        const usernameInput = this.root.querySelector<HTMLInputElement>('#login-username');
        const passwordInput = this.root.querySelector<HTMLInputElement>('#login-password');
        const loginBtn = this.root.querySelector<HTMLButtonElement>('#login-btn');

        if (usernameInput && passwordInput) {
            const username = usernameInput.value.trim();
            const password = passwordInput.value;

            // 清除之前的错误信息
            this.setState(() => ({ errorMessage: null }));

            if (!username || !password) {
                this.setState(() => ({
                    errorMessage: '请输入用户名和密码'
                }), () => {
                    this.render();
                });
                return;
            }

            // 禁用登录按钮，防止重复提交
            if (loginBtn) {
                loginBtn.disabled = true;
                loginBtn.textContent = '登录中...';
            }

            try {
                await this.handleLogin(username, password);
                // 登录成功的话，handleLogin会处理状态更新和页面跳转
            } catch (error) {
                console.error('登录提交错误:', error);
                // 错误已经在handleLogin中处理了，这里不需要额外处理
            } finally {
                // 恢复登录按钮状态（如果还在登录页面的话）
                const currentLoginBtn = this.root.querySelector<HTMLButtonElement>('#login-btn');
                if (currentLoginBtn) {
                    currentLoginBtn.disabled = false;
                    currentLoginBtn.textContent = '登录';
                }
            }
        }
    }

    // 渲染加载状态
    private renderLoading(): string {
        return `
            <div class="loading-overlay">
                <div class="loading-spinner"></div>
                <div class="loading-text">加载中...</div>
            </div>
        `;
    }

    // 渲染错误信息
    private renderError(): string {
        if (!this.state.errorMessage) return '';

        return `
            <div class="error-message">
                <span class="material-symbols-outlined">error</span>
                <span>${this.state.errorMessage}</span>
                <button class="close-error-btn">×</button>
            </div>
        `;
    }

    public render() {
        // 保存知识库搜索输入框的状态
        const searchInput = this.root.querySelector('#knowledge-search-input') as HTMLInputElement;
        let savedCursorPosition = 0;
        let hadFocus = false;
        
        if (searchInput) {
            savedCursorPosition = searchInput.selectionStart || 0;
            hadFocus = document.activeElement === searchInput;
        }

        // Detach old event listeners before re-rendering to avoid memory leaks
        this.detachEventListeners();

        // 如果用户未登录，显示登录表单
        if (!this.state.user) {
            this.root.innerHTML = this.renderLoginForm();
            this.attachEventListeners();
            return;
        }

        // 添加控制面板可见性的类名
        const controlsClass = this.state.controlsVisible ? '' : 'controls-hidden';

        const html = `
            <div class="app-container sidebar-mode-${this.state.sidebarCollapseMode} ${controlsClass}">
                ${this.renderSidebar()}
                ${this.state.sidebarCollapseMode !== 'minimal' ? this.renderNoteList() : ''}
                ${this.renderEditor()}
                ${this.state.isLoading ? this.renderLoading() : ''}
                ${this.renderError()}
                ${this.state.sidebarCollapseMode === 'minimal' ? `
                    <div class="expand-button" id="expand-button" title="展开侧边栏 (Ctrl+B)">
                        <span class="material-symbols-outlined">menu_open</span>
                    </div>

                ` : ''}
                ${this.renderPasswordChangeDialog()}
                ${this.renderKnowledgeBase()}
            </div>
        `;

        this.root.innerHTML = html;
        this.attachEventListeners();

        // Auto-focus the rename input if it exists
        const renameInput = this.root.querySelector<HTMLInputElement>('.rename-input');
        if (renameInput) {
            renameInput.focus();
            renameInput.select();
        }
    }

    // 渲染登录表单
    private renderLoginForm(): string {
        const errorDisplay = this.state.errorMessage ? 'block' : 'none';
        const errorText = this.state.errorMessage || '';

        return `
            <div class="login-container">
                <div class="login-form">
                    <h2>笔记应用登录</h2>
                    <div class="login-error" id="login-error" style="display: ${errorDisplay};">${errorText}</div>
                    <div class="form-group">
                        <label for="login-username">用户名</label>
                        <input type="text" id="login-username" placeholder="请输入用户名" autocomplete="username">
                    </div>
                    <div class="form-group">
                        <label for="login-password">密码</label>
                        <input type="password" id="login-password" placeholder="请输入密码" autocomplete="current-password">
                    </div>
                    <button id="login-btn" class="login-btn">登录</button>
                </div>
            </div>
        `;
    }

    // 删除文件夹
    private handleDeleteFolder = async (folderId: string) => {
        // 检查文件夹是否包含笔记
        try {
            const existingNotes = await api.getNotes(parseInt(folderId));
            if (existingNotes.length > 0) {
                this.setState(() => ({ errorMessage: '文件夹中有文件，无法删除' }));

                // 5秒后自动清除错误消息
                setTimeout(() => {
                    this.setState(() => ({ errorMessage: null }));
                }, 5000);

                return;
            }
        } catch (error) {
            console.error('检查文件夹内容失败:', error);
        }
        const confirmResult = await this.showConfirmDialog('确认删除?', '删除内容将进入回收站，30天后自动彻底删除。');
        if (!confirmResult) return;
        this.setState(() => ({ isLoading: true }));
        try {
            await api.deleteFolder(parseInt(folderId));
            this.setState(prev => ({
                folders: prev.folders.filter(f => f.id !== folderId),
                notes: prev.notes.filter(n => n.folderId !== folderId),
                activeFolderId: prev.activeFolderId === folderId ? null : prev.activeFolderId,
                activeNoteId: prev.activeNoteId,
                isLoading: false
            }));
        } catch (error) {
            console.error('删除文件夹失败:', error);
            this.setState(() => ({ isLoading: false, errorMessage: '删除文件夹失败' }));

            // 5秒后自动清除错误消息
            setTimeout(() => {
                this.setState(() => ({ errorMessage: null }));
            }, 5000);
        }
    }

    // 删除笔记
    private handleDeleteNote = async (noteId: string) => {
        const confirmResult = await this.showConfirmDialog('确认删除?', '删除笔记将进入回收站，30天后自动彻底删除。');
        if (!confirmResult) return;
        this.setState(() => ({ isLoading: true }));
        try {
            await api.deleteNote(parseInt(noteId));
            this.setState(prev => ({
                notes: prev.notes.filter(n => n.id !== noteId),
                activeNoteId: prev.activeNoteId === noteId ? null : prev.activeNoteId,
                isLoading: false
            }));
        } catch (error) {
            console.error('删除笔记失败:', error);
            this.setState(() => ({ isLoading: false, errorMessage: '删除笔记失败' }));
        }
    }

    // 复制笔记
    private handleDuplicateNote = async (noteId: string) => {
        try {
            const originalNote = this.state.notes.find(note => note.id === noteId);
            if (!originalNote) return;

            const duplicatedNote = await api.createNote({
                title: `${originalNote.title} - 副本`,
                content: originalNote.content,
                folderId: originalNote.folderId ? parseInt(originalNote.folderId) : undefined
            });

            // 转换API返回的note格式
            const newNote: Note = {
                id: duplicatedNote.id.toString(),
                title: duplicatedNote.title,
                content: duplicatedNote.content,
                folderId: duplicatedNote.folderId?.toString() || null,
                createdAt: duplicatedNote.createdAt,
                size: `${duplicatedNote.content.length} B`
            };

            this.setState(prevState => ({
                notes: [newNote, ...prevState.notes],
                activeNoteId: newNote.id
            }));
        } catch (error) {
            console.error('复制笔记失败:', error);
            this.setState(() => ({ errorMessage: '复制笔记失败' }));
        }
    };

    // 导出笔记
    private handleExportNote = async (noteId: string) => {
        try {
            const note = this.state.notes.find(n => n.id === noteId);
            if (!note) return;

            // 创建一个简单的文本导出
            const content = `# ${note.title}\n\n${note.content}`;
            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `${note.title}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        } catch (error) {
            console.error('导出笔记失败:', error);
            this.setState(() => ({ errorMessage: '导出笔记失败' }));
        }
    };

    // 添加自定义确认对话框方法
    private showConfirmDialog = (title: string, message: string): Promise<boolean> => {
        return new Promise(resolve => {
            const overlay = document.createElement('div');
            overlay.className = 'confirm-dialog-overlay';
            const dialog = document.createElement('div');
            dialog.className = 'confirm-dialog';
            dialog.innerHTML = `
                <div class="confirm-dialog-header">
                    <span class="material-symbols-outlined dialog-icon">warning</span>
                    <h3 class="dialog-title">${title}</h3>
                </div>
                <div class="dialog-message">${message}</div>
                <div class="dialog-buttons">
                    <button class="btn cancel-btn">取消</button>
                    <button class="btn confirm-btn">删除</button>
                </div>
            `;
            document.body.appendChild(overlay);
            document.body.appendChild(dialog);
            dialog.querySelector('.cancel-btn')!.addEventListener('click', () => {
                document.body.removeChild(dialog);
                document.body.removeChild(overlay);
                resolve(false);
            });
            dialog.querySelector('.confirm-btn')!.addEventListener('click', () => {
                document.body.removeChild(dialog);
                document.body.removeChild(overlay);
                resolve(true);
            });
        });
    }

    // --- Add new method to get saved sidebar mode ---
    private getSavedSidebarMode(): 'full' | 'partial' | 'minimal' {
        const savedMode = localStorage.getItem('sidebarCollapseMode');
        if (savedMode === 'full' || savedMode === 'partial' || savedMode === 'minimal') {
            return savedMode;
        }
        return 'full'; // Default to full if no saved preference
    }

    // 添加切换控制面板可见性的方法
    private toggleControlsVisibility = () => {
        this.setState(prev => ({
            controlsVisible: !prev.controlsVisible
        }));
    }

    // --- AI对话相关方法 ---

    // 切换AI对话面板显示
    private toggleAIChat = () => {
        this.setState(prev => ({
            aiChatVisible: !prev.aiChatVisible
        }));
    }

    // 发送消息给AI
    private sendMessageToAI = async (message: string) => {
        if (!this.aiService || !this.aiChat) {
            this.setState(() => ({
                errorMessage: 'AI服务不可用，请检查API密钥配置'
            }));
            return;
        }

        // 添加用户消息到历史
        const userMessage: AIChatMessage = {
            id: Date.now().toString(),
            role: 'user',
            content: message,
            timestamp: new Date()
        };

        this.setState(prev => ({
            aiChatMessages: [...prev.aiChatMessages, userMessage],
            aiIsGenerating: true,
            aiCurrentResponse: ''
        }));

        // 滚动到底部显示用户消息
        this.scrollToBottom();

        try {
            // 创建AI回复消息
            const aiMessageId = (Date.now() + 1).toString();
            const aiMessage: AIChatMessage = {
                id: aiMessageId,
                role: 'assistant',
                content: '',
                timestamp: new Date(),
                isStreaming: true
            };

            this.setState(prev => ({
                aiChatMessages: [...prev.aiChatMessages, aiMessage]
            }));

            // 滚动到底部显示AI消息
            this.scrollToBottom();

            // 等待DOM更新后再开始流式输出
            await new Promise(resolve => setTimeout(resolve, 50));

            // 流式接收AI回复（支持Google搜索）
            let searchMetadata: any = null;
            await this.aiService.sendMessageStream(
                this.aiChat,
                message,
                (chunk: string) => {
                    // 使用批量更新机制，减少DOM操作频率
                    this.batchUpdateStreamingMessage(aiMessageId, chunk);
                },
                (searchInfo: any) => {
                    // 保存搜索信息
                    searchMetadata = searchInfo;
                    console.log('Google搜索信息:', searchInfo);
                }
            );

            // 如果有搜索信息，保存到消息中
            if (searchMetadata) {
                const updatedMessages = [...this.state.aiChatMessages];
                const lastMessage = updatedMessages[updatedMessages.length - 1];
                if (lastMessage && lastMessage.id === aiMessageId) {
                    lastMessage.groundingMetadata = searchMetadata;

                    // 添加引用链接到内容
                    lastMessage.content = this.aiService.addCitationsToText(lastMessage.content, searchMetadata);
                }
                this.state.aiChatMessages = updatedMessages;
            }

            // 完成流式输出
            this.finishStreamingMessage(aiMessageId);
            this.setState(() => ({
                aiIsGenerating: false,
                aiCurrentResponse: ''
            }));

        } catch (error) {
            console.error('AI对话出错:', error);
            this.setState(() => ({
                aiIsGenerating: false,
                aiCurrentResponse: '',
                errorMessage: error instanceof Error ? error.message : 'AI对话出错'
            }));
        }
    }

    // 插入AI回复内容到编辑器
    private insertAIContentToEditor = (content: string) => {
        const editor = this.root.querySelector('.editor-content') as HTMLDivElement;
        if (editor && this.state.activeNoteId) {
            editor.focus();

            // 获取当前光标位置
            const selection = window.getSelection();
            if (selection && selection.rangeCount > 0) {
                const range = selection.getRangeAt(0);

                // 处理内容格式化
                const formattedContent = this.formatAIContentForEditor(content);

                // 创建包含AI内容的文档片段
                const fragment = document.createDocumentFragment();
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = formattedContent;

                // 将内容移动到文档片段中
                while (tempDiv.firstChild) {
                    fragment.appendChild(tempDiv.firstChild);
                }

                // 插入内容
                range.deleteContents();
                range.insertNode(fragment);

                // 移动光标到插入内容的末尾
                range.collapse(false);
                selection.removeAllRanges();
                selection.addRange(range);

                // 触发内容变更事件以保存
                editor.dispatchEvent(new Event('input', { bubbles: true }));

                // 显示插入成功的提示
                this.showTemporaryMessage('AI内容已插入到笔记中');
            } else {
                // 如果没有光标位置，插入到编辑器末尾
                const formattedContent = this.formatAIContentForEditor(content);
                editor.innerHTML += '<br>' + formattedContent;
                editor.dispatchEvent(new Event('input', { bubbles: true }));
                this.showTemporaryMessage('AI内容已插入到笔记末尾');
            }
        }
    }

    // 格式化AI内容用于编辑器插入
    private formatAIContentForEditor = (content: string): string => {
        return content
            .replace(/\n\n/g, '</p><p>')  // 段落分隔
            .replace(/\n/g, '<br>')       // 换行
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')  // 粗体
            .replace(/\*(.*?)\*/g, '<em>$1</em>')              // 斜体
            .replace(/`(.*?)`/g, '<code>$1</code>')            // 行内代码
            .replace(/^(.+)$/, '<p>$1</p>');                   // 包装在段落中
    }

    // 显示临时消息
    private showTemporaryMessage = (message: string) => {
        // 创建临时消息元素
        const messageEl = document.createElement('div');
        messageEl.className = 'temporary-message';
        messageEl.textContent = message;
        messageEl.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 12px 20px;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            z-index: 10000;
            font-size: 14px;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;

        document.body.appendChild(messageEl);

        // 显示动画
        setTimeout(() => {
            messageEl.style.opacity = '1';
        }, 10);

        // 3秒后自动移除
        setTimeout(() => {
            messageEl.style.opacity = '0';
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.parentNode.removeChild(messageEl);
                }
            }, 300);
        }, 3000);
    }

    // 停止AI生成
    private stopAIGeneration = () => {
        this.setState(() => ({
            aiIsGenerating: false,
            aiCurrentResponse: ''
        }));
    }

    // 滚动到聊天底部（带防抖优化）
    private scrollToBottom = () => {
        if (this.scrollTimer) {
            cancelAnimationFrame(this.scrollTimer);
        }

        this.scrollTimer = requestAnimationFrame(() => {
            const messagesContainer = this.root.querySelector('#ai-chat-messages');
            if (messagesContainer) {
                // 使用平滑滚动到底部
                messagesContainer.scrollTo({
                    top: messagesContainer.scrollHeight,
                    behavior: 'smooth'
                });

                // 备用方案：直接设置scrollTop（防止smooth不生效）
                setTimeout(() => {
                    messagesContainer.scrollTop = messagesContainer.scrollHeight;
                }, 100);
            }
            this.scrollTimer = null;
        });
    }

    // 批量更新流式消息，减少DOM操作频率
    private batchUpdateStreamingMessage = (messageId: string, chunk: string) => {
        // 累积chunk到缓冲区
        this.streamingBuffer += chunk;

        // 更新状态中的消息内容
        const updatedMessages = [...this.state.aiChatMessages];
        const lastMessage = updatedMessages[updatedMessages.length - 1];
        if (lastMessage && lastMessage.id === messageId) {
            lastMessage.content += chunk;
        }
        this.state.aiChatMessages = updatedMessages;
        this.state.aiCurrentResponse += chunk;

        // 清除之前的定时器
        if (this.streamingUpdateTimer) {
            clearTimeout(this.streamingUpdateTimer);
        }

        // 设置新的定时器，批量更新DOM
        this.streamingUpdateTimer = window.setTimeout(() => {
            this.flushStreamingUpdate();
            this.scrollToBottom();
        }, 50); // 50ms批量更新一次
    }

    // 刷新流式更新到DOM
    private flushStreamingUpdate = () => {
        const streamingElement = this.root.querySelector('#streaming-message');
        if (streamingElement && this.streamingBuffer) {
            // 获取当前显示的内容
            const currentContent = streamingElement.textContent || '';
            const contentWithoutCursor = currentContent.replace('|', '');

            // 添加缓冲区的内容
            const newContent = contentWithoutCursor + this.streamingBuffer;

            // 更新DOM
            streamingElement.innerHTML = this.formatMessageContent(newContent) + '<span class="typing-cursor">|</span>';

            // 清空缓冲区
            this.streamingBuffer = '';
        }
    }

    // 完成流式消息输出
    private finishStreamingMessage = (messageId: string) => {
        // 清除定时器和缓冲区
        if (this.streamingUpdateTimer) {
            clearTimeout(this.streamingUpdateTimer);
            this.streamingUpdateTimer = null;
        }

        // 刷新最后的缓冲内容
        this.flushStreamingUpdate();

        // 更新状态中的消息
        const updatedMessages = [...this.state.aiChatMessages];
        const lastMessage = updatedMessages[updatedMessages.length - 1];
        if (lastMessage && lastMessage.id === messageId) {
            lastMessage.isStreaming = false;
        }
        this.state.aiChatMessages = updatedMessages;

        // 移除打字机光标并更新DOM
        const streamingElement = this.root.querySelector('#streaming-message');
        if (streamingElement) {
            const currentContent = streamingElement.textContent || '';
            const contentWithoutCursor = currentContent.replace('|', '');
            streamingElement.innerHTML = this.formatMessageContent(contentWithoutCursor);
            streamingElement.classList.remove('streaming');
            streamingElement.removeAttribute('id');
        }

        // 清空缓冲区
        this.streamingBuffer = '';

        // 只更新消息区域以显示操作按钮，避免全局重新渲染
        setTimeout(() => {
            this.updateAIChatMessagesOnly();

            // 立即滚动到底部
            this.scrollToBottom();

            // 再次确保滚动位置保持在底部（防止DOM更新延迟）
            setTimeout(() => {
                this.scrollToBottom();
            }, 50);

            // 最后一次保障，确保用户看到完整的回复
            setTimeout(() => {
                this.scrollToBottom();
            }, 200);
        }, 100);
    }

    // 只更新AI聊天消息区域，避免全局重新渲染
    private updateAIChatMessagesOnly = () => {
        const messagesContainer = this.root.querySelector('#ai-chat-messages');
        if (messagesContainer) {
            messagesContainer.innerHTML = this.renderAIChatMessages();
        }
    }

    // 清空AI对话历史
    private clearAIChat = () => {
        this.setState(() => ({
            aiChatMessages: [],
            aiCurrentResponse: ''
        }));

        // 重新创建聊天会话
        if (this.aiService) {
            this.aiChat = this.aiService.createChat();
        }
    }

    // 渲染AI对话消息
    private renderAIChatMessages(): string {
        if (this.state.aiChatMessages.length === 0) {
            return `
                <div class="ai-welcome-message">
                    <div class="ai-avatar">
                        <span class="material-symbols-outlined">smart_toy</span>
                    </div>
                    <div class="ai-message-content">
                        <p>你好！我是AI助手，可以帮助你：</p>
                        <ul>
                            <li>🔍 <strong>实时搜索</strong> - 通过Google搜索获取最新信息</li>
                            <li>📝 协助写作和编辑</li>
                            <li>💡 生成创意内容</li>
                            <li>📚 解释复杂概念</li>
                            <li>🔗 提供可靠来源和引用</li>
                        </ul>
                        <p>我可以搜索最新信息来回答你的问题，有什么我可以帮助你的吗？</p>
                    </div>
                </div>
            `;
        }

        return this.state.aiChatMessages.map(message => {
            const isUser = message.role === 'user';
            const timeStr = message.timestamp.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });

            return `
                <div class="chat-message ${isUser ? 'user-message' : 'ai-message'}">
                    ${!isUser ? `
                        <div class="message-avatar">
                            <span class="material-symbols-outlined">smart_toy</span>
                        </div>
                    ` : ''}
                    <div class="message-bubble ${isUser ? 'user-bubble' : 'ai-bubble'}">
                        <div class="message-text ${message.isStreaming ? 'streaming' : ''}" ${message.isStreaming ? 'id="streaming-message"' : ''}>
                            ${this.formatMessageContent(message.content)}
                            ${message.isStreaming ? '<span class="typing-cursor">|</span>' : ''}
                        </div>
                        <div class="message-meta">
                            <span class="message-time">${timeStr}</span>
                            ${!isUser && !message.isStreaming ? `
                                <div class="message-actions">
                                    <button class="message-action-btn" data-action="insert" data-content="${this.escapeHtml(message.content)}" title="插入到笔记">
                                        <span class="material-symbols-outlined">add_box</span>
                                    </button>
                                    <button class="message-action-btn" data-action="copy" data-content="${this.escapeHtml(message.content)}" title="复制">
                                        <span class="material-symbols-outlined">content_copy</span>
                                    </button>
                                </div>
                            ` : ''}
                        </div>
                        ${!isUser && message.groundingMetadata && !message.isStreaming ? this.renderSearchInfo(message.groundingMetadata) : ''}
                    </div>
                    ${isUser ? `
                        <div class="message-avatar user-avatar">
                            <span class="material-symbols-outlined">person</span>
                        </div>
                    ` : ''}
                </div>
            `;
        }).join('');
    }

    // 渲染搜索信息
    private renderSearchInfo(groundingMetadata: any): string {
        if (!groundingMetadata) return '';

        const { webSearchQueries, groundingChunks } = groundingMetadata;

        let searchInfo = '';

        // 显示搜索查询
        if (webSearchQueries && webSearchQueries.length > 0) {
            searchInfo += `
                <div class="search-info">
                    <div class="search-queries">
                        <span class="search-icon">🔍</span>
                        <span class="search-label">搜索查询：</span>
                        ${webSearchQueries.map((query: string) => `<span class="search-query">${query}</span>`).join(', ')}
                    </div>
                </div>
            `;
        }

        // 显示搜索来源
        if (groundingChunks && groundingChunks.length > 0) {
            const sources = groundingChunks
                .filter((chunk: any) => chunk.web)
                .map((chunk: any, index: number) => {
                    const { uri, title } = chunk.web;
                    const domain = new URL(uri).hostname;
                    return `
                        <a href="${uri}" target="_blank" class="search-source" title="${title}">
                            <span class="source-number">${index + 1}</span>
                            <span class="source-domain">${domain}</span>
                        </a>
                    `;
                });

            if (sources.length > 0) {
                searchInfo += `
                    <div class="search-sources">
                        <span class="sources-label">来源：</span>
                        <div class="sources-list">
                            ${sources.join('')}
                        </div>
                    </div>
                `;
            }
        }

        return searchInfo;
    }

    // 格式化消息内容（支持简单的Markdown和引用链接）
    private formatMessageContent(content: string): string {
        return content
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            // 处理引用链接 [1](url "title")
            .replace(/\[(\d+)\]\(([^)]+)\s+"([^"]+)"\)/g, '<a href="$2" target="_blank" class="citation-link" title="$3">[$1]</a>');
    }

    // HTML转义
    private escapeHtml(text: string): string {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML.replace(/"/g, '&quot;');
    }

    // 获取关联类型的显示文本
    private getRelationTypeText(relationType: string): string {
        const typeMap: { [key: string]: string } = {
            'similar': '相似',
            'reference': '引用',
            'follow_up': '后续',
            'contradiction': '矛盾',
            'supplement': '补充'
        };
        return typeMap[relationType] || relationType;
    }

    // === 知识库功能 ===

    // 显示知识库面板
    public showKnowledgeBase = () => {
        this.setState(() => ({
            knowledgeBaseVisible: true,
            knowledgeCurrentView: 'search'
        }));
        this.render();
    }

    // 隐藏知识库面板
    public hideKnowledgeBase = () => {
        this.setState(() => ({
            knowledgeBaseVisible: false
        }));
        this.render();
    }

    // 切换知识库视图
    public switchKnowledgeView = (view: 'search' | 'analysis' | 'relations' | 'stats') => {
        this.setState(() => ({
            knowledgeCurrentView: view
        }));

        // 如果切换到分析视图且有选中的笔记，加载分析结果
        if (view === 'analysis' && this.state.activeNoteId) {
            this.loadNoteAnalysis(this.state.activeNoteId);
        }
        
        // 如果切换到关联发现视图且有选中的笔记，加载关联信息
        if (view === 'relations' && this.state.activeNoteId) {
            this.loadNoteRelations(this.state.activeNoteId);
        }
        
        // 如果切换到统计分析视图，加载统计信息
        if (view === 'stats') {
            this.loadUserStats();
        }

        this.render();
    }

    // 更新搜索查询
    public updateSearchQuery = (query: string) => {
        console.log('updateSearchQuery called with:', query);
        console.log('Previous query:', this.state.knowledgeSearchQuery);

        // 直接更新状态，不触发重新渲染
        this.state.knowledgeSearchQuery = query;

        console.log('Search query updated to:', query);
    }

    // 设置搜索类型
    public setSearchType = (searchType: 'semantic' | 'keyword' | 'hybrid') => {
        console.log('setSearchType called with:', searchType);
        console.log('Previous search type:', this.state.knowledgeSearchType);
        
        this.setState(() => ({
            knowledgeSearchType: searchType
        }));
        
        console.log('Search type updated to:', searchType);
        this.render();
    }

    // 执行知识库搜索
    public performKnowledgeSearch = async (query: string, searchType: 'semantic' | 'keyword' | 'hybrid' = 'semantic') => {
        console.log('performKnowledgeSearch called with:', { query, searchType });

        if (!query.trim()) {
            console.log('Empty query detected, preventing search execution');
            console.log('Search query is empty or contains only whitespace');
            return;
        }

        this.setState(() => ({
            knowledgeSearchLoading: true,
            knowledgeSearchQuery: query,
            knowledgeSearchType: searchType
        }));
        
        console.log('Search state updated:', {
            loading: true,
            query: query,
            searchType: searchType
        });
        
        this.render();
        console.log('Render called after state update');

        try {
            console.log('Sending search request to API...');
            console.log('Request URL:', `${api.getBaseUrl()}/knowledge/search`);
            console.log('Request payload:', {
                query,
                searchType,
                options: {
                    limit: 20,
                    threshold: 0.7,
                    includeContent: false
                }
            });
            const response = await fetch(`${api.getBaseUrl()}/knowledge/search`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${api.getToken()}`
                },
                body: JSON.stringify({
                    query,
                    searchType,
                    options: {
                        limit: 20,
                        threshold: 0.7,
                        includeContent: false
                    }
                })
            });

            if (!response.ok) {
                throw new Error('搜索失败');
            }

            const result = await response.json();
            console.log('Search response:', result);
            console.log('Search results:', result.data?.results);

            // 后端返回格式: { success: true, data: { results: [...], totalCount: 4, searchTime: 6 } }
            const searchResults = result.data?.results || [];
            console.log('Parsed search results:', searchResults);
            console.log('Search results length:', searchResults.length);
            console.log('Current state before update:', {
                knowledgeSearchResults: this.state.knowledgeSearchResults,
                knowledgeSearchLoading: this.state.knowledgeSearchLoading
            });

            this.setState(() => ({
                knowledgeSearchResults: searchResults,
                knowledgeSearchLoading: false
            }));

            console.log('State updated, calling render...');
            this.render();

            // 验证状态更新后的值
            setTimeout(() => {
                console.log('State after render:', {
                    knowledgeSearchResults: this.state.knowledgeSearchResults,
                    knowledgeSearchLoading: this.state.knowledgeSearchLoading
                });
            }, 100);

        } catch (error) {
            console.error('Knowledge search error:', error);

            // 如果是语义搜索失败，自动降级到关键词搜索
            if (searchType === 'semantic' && error.message.includes('embedding')) {
                console.log('语义搜索失败，降级到关键词搜索');
                try {
                    const fallbackResponse = await fetch(`${api.getBaseUrl()}/knowledge/search`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${api.getToken()}`
                        },
                        body: JSON.stringify({
                            query,
                            searchType: 'keyword',
                            options: {
                                limit: 20,
                                threshold: 0.7,
                                includeContent: false
                            }
                        })
                    });

                    if (fallbackResponse.ok) {
                        const fallbackResult = await fallbackResponse.json();
                        const fallbackResults = fallbackResult.data?.results || [];
                        console.log('Fallback search results:', fallbackResults);

                        this.setState(() => ({
                            knowledgeSearchResults: fallbackResults,
                            knowledgeSearchLoading: false,
                            knowledgeSearchType: 'keyword',
                            errorMessage: '语义搜索暂时不可用，已切换到关键词搜索'
                        }));
                        this.render();
                        return;
                    }
                } catch (fallbackError) {
                    console.error('关键词搜索也失败了:', fallbackError);
                }
            }

            this.setState(() => ({
                knowledgeSearchResults: [],
                knowledgeSearchLoading: false,
                errorMessage: '搜索失败，请稍后重试。如果问题持续，请检查网络连接。'
            }));
            this.render();
        }
    }

    // 加载笔记分析结果（如果存在）
    private loadNoteAnalysis = async (noteId: string) => {
        try {
            const response = await fetch(`${api.getBaseUrl()}/knowledge/analysis/${noteId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${api.getToken()}`
                }
            });

            if (response.ok) {
                const result = await response.json();
                this.setState(() => ({
                    currentNoteAnalysis: result.data
                }));
                if (this.state.knowledgeCurrentView === 'analysis') {
                    this.render();
                }
            } else {
                // 没有分析结果，清空当前分析
                this.setState(() => ({
                    currentNoteAnalysis: null
                }));
                if (this.state.knowledgeCurrentView === 'analysis') {
                    this.render();
                }
            }
        } catch (error) {
            console.error('Load analysis error:', error);
            this.setState(() => ({
                currentNoteAnalysis: null
            }));
        }
    }

    // 分析当前笔记
    public analyzeCurrentNote = async () => {
        if (!this.state.activeNoteId) {
            this.setState(() => ({
                errorMessage: '请先选择一个笔记'
            }));
            this.render();
            return;
        }

        this.setState(() => ({
            analysisLoading: true,
            knowledgeCurrentView: 'analysis',
            errorMessage: null
        }));
        this.render();

        try {
            // 执行分析
            const analyzeResponse = await fetch(`${api.getBaseUrl()}/knowledge/analyze/${this.state.activeNoteId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${api.getToken()}`
                }
            });

            if (!analyzeResponse.ok) {
                throw new Error('分析失败');
            }

            const analyzeResult = await analyzeResponse.json();
            console.log('Note analysis completed:', analyzeResult.data);

            // 获取分析结果
            const getResponse = await fetch(`${api.getBaseUrl()}/knowledge/analysis/${this.state.activeNoteId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${api.getToken()}`
                }
            });

            if (!getResponse.ok) {
                throw new Error('获取分析结果失败');
            }

            const analysisResult = await getResponse.json();
            console.log('Note analysis result:', analysisResult.data);

            this.setState(() => ({
                currentNoteAnalysis: analysisResult.data,
                analysisLoading: false
            }));
            this.render();

        } catch (error) {
            console.error('Note analysis error:', error);
            this.setState(() => ({
                analysisLoading: false,
                errorMessage: error instanceof Error ? error.message : '分析失败，请稍后重试'
            }));
            this.render();
        }
    }

    // 加载笔记关联信息
    private loadNoteRelations = async (noteId: string) => {
        try {
            this.setState(() => ({
                relationsLoading: true
            }));

            const response = await fetch(`${api.getBaseUrl()}/knowledge/relations/${noteId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${api.getToken()}`
                }
            });

            if (response.ok) {
                const result = await response.json();
                this.setState(() => ({
                    noteRelations: result.data,
                    relationsLoading: false
                }));
            } else {
                this.setState(() => ({
                    noteRelations: null,
                    relationsLoading: false
                }));
            }

            if (this.state.knowledgeCurrentView === 'relations') {
                this.render();
            }
        } catch (error) {
            console.error('Load relations error:', error);
            this.setState(() => ({
                noteRelations: null,
                relationsLoading: false,
                errorMessage: '加载关联信息失败'
            }));
            if (this.state.knowledgeCurrentView === 'relations') {
                this.render();
            }
        }
    }

    // 加载用户统计信息
    private loadUserStats = async () => {
        try {
            this.setState(() => ({
                statsLoading: true
            }));

            const response = await fetch(`${api.getBaseUrl()}/knowledge/stats`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${api.getToken()}`
                }
            });

            if (response.ok) {
                const result = await response.json();
                this.setState(() => ({
                    userStats: result.data,
                    statsLoading: false
                }));
            } else {
                this.setState(() => ({
                    userStats: null,
                    statsLoading: false
                }));
            }

            if (this.state.knowledgeCurrentView === 'stats') {
                this.render();
            }
        } catch (error) {
            console.error('Load stats error:', error);
            this.setState(() => ({
                userStats: null,
                statsLoading: false,
                errorMessage: '加载统计信息失败'
            }));
            if (this.state.knowledgeCurrentView === 'stats') {
                this.render();
            }
        }
    }

    // 选择笔记（供全局调用）
    public selectNote = (noteId: string) => {
        this.handleSelectNote(noteId);
        // 关闭知识库面板
        this.hideKnowledgeBase();
    }

    // 批量处理笔记
    public batchProcessNotes = async () => {
        // 确认用户是否要执行批量处理
        const confirmed = confirm(
            '批量处理将对所有笔记进行向量化、分析、关联发现和标签管理。\n' +
            '这个过程可能需要几分钟时间，期间请不要关闭页面。\n\n' +
            '是否继续？'
        );

        if (!confirmed) return;

        this.setState(() => ({
            errorMessage: '正在执行批量处理，请耐心等待...'
        }));
        this.render();

        try {
            console.log('开始批量处理...');
            const startTime = Date.now();

            const response = await fetch(`${api.getBaseUrl()}/knowledge/batch/process`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${api.getToken()}`
                },
                body: JSON.stringify({
                    operations: ['vectorize', 'analyze', 'relations', 'tags']
                })
            });

            if (!response.ok) {
                throw new Error('批量处理失败');
            }

            const result = await response.json();
            const processingTime = Math.round((Date.now() - startTime) / 1000);

            console.log('Batch processing result:', result.data);
            console.log(`批量处理完成，耗时: ${processingTime}秒`);

            this.setState(() => ({
                errorMessage: `批量处理完成！耗时 ${processingTime} 秒。现在可以使用语义搜索功能了。`
            }));
            this.render();

            // 3秒后清除消息
            setTimeout(() => {
                this.setState(() => ({ errorMessage: null }));
                this.render();
            }, 3000);

        } catch (error) {
            console.error('Batch processing error:', error);
            this.setState(() => ({
                errorMessage: '批量处理失败，请稍后重试。如果问题持续，请检查网络连接。'
            }));
            this.render();
        }
    }

    // 渲染知识库面板
    private renderKnowledgeBase(): string {
        if (!this.state.knowledgeBaseVisible) return '';

        return `
            <div class="knowledge-base-container visible">
                <div class="knowledge-base-panel">
                    <div class="knowledge-base-header">
                        <h2 class="knowledge-base-title">
                            <span class="material-symbols-outlined">psychology</span>
                            智能知识库
                        </h2>
                        <button class="knowledge-base-close" onclick="app.hideKnowledgeBase()">
                            <span class="material-symbols-outlined">close</span>
                        </button>
                    </div>

                    <div class="knowledge-base-content">
                        <div class="knowledge-sidebar">
                            <div class="knowledge-sidebar-nav">
                                <div class="knowledge-nav-item ${this.state.knowledgeCurrentView === 'search' ? 'active' : ''}"
                                     onclick="app.switchKnowledgeView('search')">
                                    <span class="material-symbols-outlined">search</span>
                                    语义搜索
                                </div>
                                <div class="knowledge-nav-item ${this.state.knowledgeCurrentView === 'analysis' ? 'active' : ''}"
                                     onclick="app.switchKnowledgeView('analysis')">
                                    <span class="material-symbols-outlined">analytics</span>
                                    内容分析
                                </div>
                                <div class="knowledge-nav-item ${this.state.knowledgeCurrentView === 'relations' ? 'active' : ''}"
                                     onclick="app.switchKnowledgeView('relations')">
                                    <span class="material-symbols-outlined">hub</span>
                                    关联发现
                                </div>
                                <div class="knowledge-nav-item ${this.state.knowledgeCurrentView === 'stats' ? 'active' : ''}"
                                     onclick="app.switchKnowledgeView('stats')">
                                    <span class="material-symbols-outlined">bar_chart</span>
                                    统计分析
                                </div>
                            </div>
                        </div>

                        <div class="knowledge-main">
                            ${this.renderKnowledgeMainContent()}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // 渲染知识库主内容
    private renderKnowledgeMainContent(): string {
        switch (this.state.knowledgeCurrentView) {
            case 'search':
                return this.renderKnowledgeSearch();
            case 'analysis':
                return this.renderKnowledgeAnalysis();
            case 'relations':
                return this.renderKnowledgeRelations();
            case 'stats':
                return this.renderKnowledgeStats();
            default:
                return this.renderKnowledgeSearch();
        }
    }

    // 渲染搜索界面
    private renderKnowledgeSearch(): string {
        return `
            <div class="knowledge-search-section">
                <div class="knowledge-search-container">
                    <span class="material-symbols-outlined knowledge-search-icon">search</span>
                    <input type="text"
                           id="knowledge-search-input"
                           class="knowledge-search-input"
                           placeholder="输入搜索内容，支持语义搜索..."
                           value="${this.escapeHtml(this.state.knowledgeSearchQuery)}"
                           oninput="app.updateSearchQuery(this.value)"
                           onkeypress="if(event.key==='Enter') app.performKnowledgeSearch(this.value, '${this.state.knowledgeSearchType}')">
                </div>

                <div class="knowledge-search-options">
                    <div class="knowledge-search-type">
                        <button class="search-type-btn ${this.state.knowledgeSearchType === 'semantic' ? 'active' : ''}"
                                onclick="app.setSearchType('semantic')">
                            语义搜索
                        </button>
                        <button class="search-type-btn ${this.state.knowledgeSearchType === 'keyword' ? 'active' : ''}"
                                onclick="app.setSearchType('keyword')">
                            关键词搜索
                        </button>
                        <button class="search-type-btn ${this.state.knowledgeSearchType === 'hybrid' ? 'active' : ''}"
                                onclick="app.setSearchType('hybrid')">
                            混合搜索
                        </button>
                    </div>

                    <button class="search-type-btn" onclick="app.performKnowledgeSearch(document.getElementById('knowledge-search-input').value, '${this.state.knowledgeSearchType}')" style="margin-right: 10px;">
                        <span class="material-symbols-outlined" style="font-size: 16px; margin-right: 4px;">search</span>
                        搜索
                    </button>

                    <button class="search-type-btn" onclick="app.batchProcessNotes()" style="margin-left: auto;">
                        <span class="material-symbols-outlined" style="font-size: 16px; margin-right: 4px;">auto_fix_high</span>
                        批量处理
                    </button>
                </div>
            </div>

            <div class="knowledge-content-scroll">
                <div class="knowledge-results">
                    ${this.renderKnowledgeSearchResults()}
                </div>
            </div>
        `;
    }

    // 渲染搜索结果
    private renderKnowledgeSearchResults(): string {
        console.log('renderKnowledgeSearchResults called with:', {
            loading: this.state.knowledgeSearchLoading,
            resultsLength: this.state.knowledgeSearchResults.length,
            results: this.state.knowledgeSearchResults
        });

        if (this.state.knowledgeSearchLoading) {
            console.log('Rendering loading state');
            return `
                <div class="knowledge-loading">
                    <div class="knowledge-loading-spinner"></div>
                    <div>正在搜索...</div>
                </div>
            `;
        }

        if (this.state.knowledgeSearchResults.length === 0) {
            console.log('Rendering empty state');
            if (this.state.knowledgeSearchQuery) {
                return `
                    <div class="knowledge-empty">
                        <span class="material-symbols-outlined knowledge-empty-icon">search_off</span>
                        <div class="knowledge-empty-title">未找到相关内容</div>
                        <div class="knowledge-empty-description">
                            尝试使用不同的关键词或切换搜索类型
                        </div>
                    </div>
                `;
            } else {
                return `
                    <div class="knowledge-empty">
                        <span class="material-symbols-outlined knowledge-empty-icon">psychology</span>
                        <div class="knowledge-empty-title">开始智能搜索</div>
                        <div class="knowledge-empty-description">
                            输入搜索内容，体验基于AI的语义搜索功能
                        </div>
                    </div>
                `;
            }
        }

        console.log('Rendering search results:', this.state.knowledgeSearchResults.length, 'items');

        return `
            <div class="knowledge-results-header">
                <div class="knowledge-results-count">
                    找到 ${this.state.knowledgeSearchResults.length} 个相关结果
                </div>
            </div>

            ${this.state.knowledgeSearchResults.map((result, index) => {
            console.log(`Rendering result ${index}:`, result);
            return `
                <div class="knowledge-result-item" onclick="app.selectNote('${result.noteId}')">
                    <div class="knowledge-result-header">
                        <h3 class="knowledge-result-title">${this.escapeHtml(result.title)}</h3>
                        ${result.similarity ? `<span class="knowledge-result-score">${Math.round(result.similarity * 100)}%</span>` : ''}
                    </div>

                    <div class="knowledge-result-preview">
                        ${this.escapeHtml(result.matchedText || '').substring(0, 200)}...
                    </div>

                    <div class="knowledge-result-meta">
                        <div class="knowledge-result-folder">
                            <span class="material-symbols-outlined" style="font-size: 14px;">folder</span>
                            ${result.folderId ? '文件夹' : '根目录'}
                        </div>
                        <div class="knowledge-result-date">
                            <span class="material-symbols-outlined" style="font-size: 14px;">schedule</span>
                            ${new Date(result.updatedAt).toLocaleDateString()}
                        </div>
                    </div>
                </div>
                `;
        }).join('')}
        `;
    }

    // 渲染分析界面
    private renderKnowledgeAnalysis(): string {
        const currentNote = this.state.notes.find(note => note.id === this.state.activeNoteId);
        const noteTitle = currentNote ? currentNote.title : '未选择笔记';

        return `
            <div class="knowledge-analysis-section">
                <div class="analysis-header" style="padding: 20px; border-bottom: 1px solid #e5e7eb; flex-shrink: 0;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                        <h3 style="margin: 0; color: #1f2937;">内容分析</h3>
                        <button class="search-type-btn ${this.state.analysisLoading ? 'loading' : ''}"
                                onclick="app.analyzeCurrentNote()"
                                ${this.state.analysisLoading ? 'disabled' : ''}
                                style="padding: 8px 16px;">
                            <span class="material-symbols-outlined" style="font-size: 16px; margin-right: 8px;">
                                ${this.state.analysisLoading ? 'hourglass_empty' : 'analytics'}
                            </span>
                            ${this.state.analysisLoading ? '分析中...' : '分析当前笔记'}
                        </button>
                    </div>
                    <div style="color: #6b7280; font-size: 14px;">
                        当前笔记: ${this.escapeHtml(noteTitle)}
                    </div>
                </div>

                <div class="knowledge-content-scroll">
                    ${this.state.analysisLoading ? this.renderAnalysisLoading() : ''}
                    ${this.state.currentNoteAnalysis ? this.renderAnalysisResults() : this.renderAnalysisEmpty()}
                </div>
            </div>
        `;
    }

    // 渲染分析加载状态
    private renderAnalysisLoading(): string {
        return `
            <div class="analysis-loading" style="padding: 40px; text-align: center;">
                <div class="loading-spinner" style="margin: 0 auto 16px; width: 32px; height: 32px; border: 3px solid #e5e7eb; border-top: 3px solid #4f46e5; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                <div style="color: #6b7280;">正在分析笔记内容，请稍候...</div>
            </div>
        `;
    }

    // 渲染分析结果
    private renderAnalysisResults(): string {
        const analysis = this.state.currentNoteAnalysis;
        if (!analysis) return '';

        const keywords = Array.isArray(analysis.keywords) ? analysis.keywords :
            (typeof analysis.keywords === 'string' ? JSON.parse(analysis.keywords) : []);
        const topics = Array.isArray(analysis.topics) ? analysis.topics :
            (typeof analysis.topics === 'string' ? JSON.parse(analysis.topics) : []);

        return `
            <div class="analysis-results" style="padding: 20px; padding-bottom: 40px;">
                <!-- 摘要 -->
                <div class="analysis-card">
                    <div class="analysis-card-title">
                        <span class="material-symbols-outlined">summarize</span>
                        内容摘要
                    </div>
                    <div class="analysis-summary" style="color: #374151; line-height: 1.6;">
                        ${this.escapeHtml(analysis.summary || '暂无摘要')}
                    </div>
                </div>

                <!-- 关键词 -->
                <div class="analysis-card">
                    <div class="analysis-card-title">
                        <span class="material-symbols-outlined">label</span>
                        关键词
                    </div>
                    <div class="keywords-container" style="display: flex; flex-wrap: wrap; gap: 8px;">
                        ${keywords.map(keyword => `
                            <span class="keyword-tag" style="background: #eff6ff; color: #1d4ed8; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                                ${this.escapeHtml(keyword)}
                            </span>
                        `).join('')}
                    </div>
                </div>

                <!-- 主题分类 -->
                <div class="analysis-card">
                    <div class="analysis-card-title">
                        <span class="material-symbols-outlined">category</span>
                        主题分类
                    </div>
                    <div class="topics-container" style="display: flex; flex-wrap: wrap; gap: 8px;">
                        ${topics.map(topic => `
                            <span class="topic-tag" style="background: #f0fdf4; color: #166534; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                                ${this.escapeHtml(topic)}
                            </span>
                        `).join('')}
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="analysis-card">
                    <div class="analysis-card-title">
                        <span class="material-symbols-outlined">analytics</span>
                        统计信息
                    </div>
                    <div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 16px;">
                        <div class="stat-item" style="text-align: center;">
                            <div style="font-size: 24px; font-weight: 600; color: #1f2937;">${analysis.word_count || 0}</div>
                            <div style="font-size: 12px; color: #6b7280;">字数</div>
                        </div>
                        <div class="stat-item" style="text-align: center;">
                            <div style="font-size: 24px; font-weight: 600; color: #1f2937;">${analysis.char_count || 0}</div>
                            <div style="font-size: 12px; color: #6b7280;">字符数</div>
                        </div>
                        <div class="stat-item" style="text-align: center;">
                            <div style="font-size: 24px; font-weight: 600; color: #1f2937;">${this.escapeHtml(analysis.category || '未分类')}</div>
                            <div style="font-size: 12px; color: #6b7280;">分类</div>
                        </div>
                        <div class="stat-item" style="text-align: center;">
                            <div style="font-size: 24px; font-weight: 600; color: #1f2937;">${Math.round((analysis.confidence_score || 0) * 100)}%</div>
                            <div style="font-size: 12px; color: #6b7280;">置信度</div>
                        </div>
                    </div>
                </div>

                <!-- 分析时间和完成标识 -->
                ${analysis.analyzed_at ? `
                    <div style="margin-top: 24px; padding: 16px; border-top: 1px solid #e5e7eb; background: #f8fafc; border-radius: 8px;">
                        <div style="text-align: center; color: #6b7280; font-size: 12px; margin-bottom: 8px;">
                            分析时间: ${new Date(analysis.analyzed_at).toLocaleString()}
                        </div>
                        <div style="text-align: center; color: #10b981; font-size: 12px; display: flex; align-items: center; justify-content: center; gap: 4px;">
                            <span class="material-symbols-outlined" style="font-size: 16px;">check_circle</span>
                            分析完成
                        </div>
                    </div>
                ` : ''}

                <!-- 底部间距 -->
                <div style="height: 40px;"></div>
            </div>
        `;
    }

    // 渲染空状态
    private renderAnalysisEmpty(): string {
        if (!this.state.activeNoteId) {
            return `
                <div class="knowledge-empty" style="padding: 60px 20px;">
                    <span class="material-symbols-outlined knowledge-empty-icon">note_alt</span>
                    <div class="knowledge-empty-title">请选择笔记</div>
                    <div class="knowledge-empty-description">
                        请先选择一个笔记，然后点击"分析当前笔记"按钮
                    </div>
                </div>
            `;
        }

        return `
            <div class="knowledge-empty" style="padding: 60px 20px;">
                <span class="material-symbols-outlined knowledge-empty-icon">analytics</span>
                <div class="knowledge-empty-title">暂无分析结果</div>
                <div class="knowledge-empty-description">
                    点击"分析当前笔记"按钮开始AI分析，获取关键词、主题、摘要等信息
                </div>
            </div>
        `;
    }

    // 渲染关联界面
    private renderKnowledgeRelations(): string {
        if (!this.state.activeNoteId) {
            return `
                <div class="knowledge-relations-section">
                    <div class="knowledge-content-scroll">
                        <div class="knowledge-empty">
                            <span class="material-symbols-outlined knowledge-empty-icon">hub</span>
                            <div class="knowledge-empty-title">关联发现</div>
                            <div class="knowledge-empty-description">
                                请先选择一个笔记来查看其关联信息
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        if (this.state.relationsLoading) {
            return `
                <div class="knowledge-relations-section">
                    <div class="knowledge-content-scroll">
                        <div class="knowledge-loading">
                            <div class="loading-spinner"></div>
                            <div>正在分析笔记关联...</div>
                        </div>
                    </div>
                </div>
            `;
        }

        const relations = this.state.noteRelations;
        if (!relations || (!relations.outgoing?.length && !relations.incoming?.length)) {
            return `
                <div class="knowledge-relations-section">
                    <div class="knowledge-content-scroll">
                        <div class="knowledge-empty">
                            <span class="material-symbols-outlined knowledge-empty-icon">hub</span>
                            <div class="knowledge-empty-title">暂无关联</div>
                            <div class="knowledge-empty-description">
                                当前笔记暂未发现关联内容，可以尝试批量处理来发现更多关联
                            </div>
                            <button class="search-type-btn" onclick="app.batchProcessNotes()" style="margin-top: 16px;">
                                <span class="material-symbols-outlined" style="font-size: 16px; margin-right: 4px;">auto_fix_high</span>
                                批量处理
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        return `
            <div class="knowledge-relations-section">
                <div class="knowledge-content-scroll">
                    <div style="padding: 20px; padding-bottom: 40px;">
                        ${relations.outgoing?.length > 0 ? `
                            <div class="relations-card">
                                <div class="relations-card-title">
                                    <span class="material-symbols-outlined">arrow_forward</span>
                                    相关笔记 (${relations.outgoing.length})
                                </div>
                                <div class="relations-list">
                                    ${relations.outgoing.map(relation => `
                                        <div class="relation-item" onclick="app.selectNote('${relation.noteId}')">
                                            <div class="relation-content">
                                                <div class="relation-title">${this.escapeHtml(relation.title)}</div>
                                                <div class="relation-meta">
                                                    <span class="relation-type">${this.getRelationTypeText(relation.relationType)}</span>
                                                    <span class="relation-similarity">相似度: ${Math.round(relation.similarity * 100)}%</span>
                                                </div>
                                                ${relation.reason ? `<div class="relation-reason">${this.escapeHtml(relation.reason)}</div>` : ''}
                                            </div>
                                            <span class="material-symbols-outlined relation-arrow">chevron_right</span>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        ` : ''}
                        
                        ${relations.incoming?.length > 0 ? `
                            <div class="relations-card">
                                <div class="relations-card-title">
                                    <span class="material-symbols-outlined">arrow_back</span>
                                    引用此笔记 (${relations.incoming.length})
                                </div>
                                <div class="relations-list">
                                    ${relations.incoming.map(relation => `
                                        <div class="relation-item" onclick="app.selectNote('${relation.noteId}')">
                                            <div class="relation-content">
                                                <div class="relation-title">${this.escapeHtml(relation.title)}</div>
                                                <div class="relation-meta">
                                                    <span class="relation-type">${this.getRelationTypeText(relation.relationType)}</span>
                                                    <span class="relation-similarity">相似度: ${Math.round(relation.similarity * 100)}%</span>
                                                </div>
                                                ${relation.reason ? `<div class="relation-reason">${this.escapeHtml(relation.reason)}</div>` : ''}
                                            </div>
                                            <span class="material-symbols-outlined relation-arrow">chevron_right</span>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    }

    // 获取关联类型文本
    private getRelationTypeText(type: string): string {
        const typeMap: { [key: string]: string } = {
            'similar': '相似',
            'reference': '引用',
            'follow_up': '后续',
            'contradiction': '矛盾',
            'supplement': '补充'
        };
        return typeMap[type] || type;
    }

    // 渲染统计界面
    private renderKnowledgeStats(): string {
        if (this.state.statsLoading) {
            return `
                <div class="knowledge-stats-section">
                    <div class="knowledge-content-scroll">
                        <div class="knowledge-loading">
                            <div class="loading-spinner"></div>
                            <div>正在加载统计信息...</div>
                        </div>
                    </div>
                </div>
            `;
        }

        const stats = this.state.userStats;
        if (!stats || stats.totalAnalyzed === 0) {
            return `
                <div class="knowledge-stats-section">
                    <div class="knowledge-content-scroll">
                        <div class="knowledge-empty">
                            <span class="material-symbols-outlined knowledge-empty-icon">bar_chart</span>
                            <div class="knowledge-empty-title">暂无统计数据</div>
                            <div class="knowledge-empty-description">
                                需要先分析笔记内容才能查看统计信息
                            </div>
                            <button class="search-type-btn" onclick="app.batchProcessNotes()" style="margin-top: 16px;">
                                <span class="material-symbols-outlined" style="font-size: 16px; margin-right: 4px;">auto_fix_high</span>
                                批量处理
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        return `
            <div class="knowledge-stats-section">
                <div class="knowledge-content-scroll">
                    <div style="padding: 20px; padding-bottom: 40px;">
                        <!-- 总体统计 -->
                        <div class="stats-card">
                            <div class="stats-card-title">
                                <span class="material-symbols-outlined">analytics</span>
                                总体统计
                            </div>
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-value">${stats.totalAnalyzed}</div>
                                    <div class="stat-label">已分析笔记</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">${Math.round(stats.avgWordCount || 0)}</div>
                                    <div class="stat-label">平均字数</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">${this.getSentimentText(stats.avgSentiment || 0)}</div>
                                    <div class="stat-label">整体情感</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">${Math.round((stats.avgComplexity || 0) * 100)}%</div>
                                    <div class="stat-label">平均复杂度</div>
                                </div>
                            </div>
                        </div>

                        <!-- 分类分布 -->
                        ${stats.categories && stats.categories.length > 0 ? `
                            <div class="stats-card">
                                <div class="stats-card-title">
                                    <span class="material-symbols-outlined">category</span>
                                    分类分布
                                </div>
                                <div class="categories-chart">
                                    ${stats.categories.map(cat => {
                                        const percentage = Math.round((cat.count / stats.totalAnalyzed) * 100);
                                        return `
                                            <div class="category-item">
                                                <div class="category-info">
                                                    <span class="category-name">${this.escapeHtml(cat.category)}</span>
                                                    <span class="category-count">${cat.count} (${percentage}%)</span>
                                                </div>
                                                <div class="category-bar">
                                                    <div class="category-fill" style="width: ${percentage}%"></div>
                                                </div>
                                            </div>
                                        `;
                                    }).join('')}
                                </div>
                            </div>
                        ` : ''}

                        <!-- 热门关键词 -->
                        ${stats.topKeywords && stats.topKeywords.length > 0 ? `
                            <div class="stats-card">
                                <div class="stats-card-title">
                                    <span class="material-symbols-outlined">label</span>
                                    热门关键词
                                </div>
                                <div class="keywords-cloud">
                                    ${stats.topKeywords.map(kw => {
                                        const size = Math.min(Math.max(kw.count / 2 + 12, 12), 24);
                                        return `
                                            <span class="keyword-cloud-item" style="font-size: ${size}px;">
                                                ${this.escapeHtml(kw.keyword)}
                                                <span class="keyword-count">(${kw.count})</span>
                                            </span>
                                        `;
                                    }).join('')}
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    }

    // 获取情感文本描述
    private getSentimentText(score: number): string {
        if (score > 0.1) return '积极';
        if (score < -0.1) return '消极';
        return '中性';
    }

    // 显示选择工具栏
    private showSelectionToolbar(x: number, y: number, selectedText: string) {
        // 移除现有的工具栏
        this.hideSelectionToolbar();

        const toolbar = document.createElement('div');
        toolbar.className = 'selection-toolbar';
        toolbar.id = 'selection-toolbar';

        toolbar.innerHTML = `
            <button class="selection-btn" data-action="ask-ai" title="询问AI">
                <span class="material-symbols-outlined">smart_toy</span>
                <span>询问AI</span>
            </button>
            <button class="selection-btn" data-action="explain" title="解释这段内容">
                <span class="material-symbols-outlined">help</span>
                <span>解释</span>
            </button>
            <button class="selection-btn" data-action="summarize" title="总结">
                <span class="material-symbols-outlined">summarize</span>
                <span>总结</span>
            </button>
            <button class="selection-btn" data-action="translate" title="翻译">
                <span class="material-symbols-outlined">translate</span>
                <span>翻译</span>
            </button>
        `;

        // 定位工具栏
        toolbar.style.position = 'fixed';
        toolbar.style.left = `${x}px`;
        toolbar.style.top = `${y - 60}px`;
        toolbar.style.zIndex = '1000';

        document.body.appendChild(toolbar);

        // 添加事件监听器
        toolbar.addEventListener('click', (e) => {
            const target = e.target as HTMLElement;
            const button = target.closest('.selection-btn') as HTMLElement;
            if (button) {
                const action = button.dataset.action;
                this.handleSelectionAction(action!, selectedText);
                this.hideSelectionToolbar();
            }
        });

        // 点击其他地方隐藏工具栏
        const hideOnClick = (e: MouseEvent) => {
            if (!toolbar.contains(e.target as Node)) {
                this.hideSelectionToolbar();
                document.removeEventListener('click', hideOnClick);
            }
        };

        setTimeout(() => {
            document.addEventListener('click', hideOnClick);
        }, 100);
    }

    // 隐藏选择工具栏
    private hideSelectionToolbar() {
        const toolbar = document.getElementById('selection-toolbar');
        if (toolbar) {
            document.body.removeChild(toolbar);
        }
    }

    // 显示右键菜单
    private showContextMenu(x: number, y: number, selectedText: string) {
        const contextMenu = document.createElement('div');
        contextMenu.className = 'context-menu';
        contextMenu.id = 'context-menu';

        contextMenu.innerHTML = `
            <div class="context-menu-item" data-action="ask-ai">
                <span class="material-symbols-outlined">smart_toy</span>
                <span>询问AI</span>
            </div>
            <div class="context-menu-item" data-action="explain">
                <span class="material-symbols-outlined">help</span>
                <span>解释这段内容</span>
            </div>
            <div class="context-menu-item" data-action="summarize">
                <span class="material-symbols-outlined">summarize</span>
                <span>总结</span>
            </div>
            <div class="context-menu-item" data-action="translate">
                <span class="material-symbols-outlined">translate</span>
                <span>翻译</span>
            </div>
            <div class="context-menu-separator"></div>
            <div class="context-menu-item" data-action="copy">
                <span class="material-symbols-outlined">content_copy</span>
                <span>复制</span>
            </div>
        `;

        // 定位菜单
        contextMenu.style.position = 'fixed';
        contextMenu.style.left = `${x}px`;
        contextMenu.style.top = `${y}px`;
        contextMenu.style.zIndex = '1001';

        document.body.appendChild(contextMenu);

        // 添加事件监听器
        contextMenu.addEventListener('click', (e) => {
            const target = e.target as HTMLElement;
            const item = target.closest('.context-menu-item') as HTMLElement;
            if (item) {
                const action = item.dataset.action;
                if (action === 'copy') {
                    navigator.clipboard.writeText(selectedText);
                    this.showTemporaryMessage('已复制到剪贴板');
                } else {
                    this.handleSelectionAction(action!, selectedText);
                }
                this.hideContextMenu();
            }
        });

        // 点击其他地方隐藏菜单
        const hideOnClick = (e: MouseEvent) => {
            if (!contextMenu.contains(e.target as Node)) {
                this.hideContextMenu();
                document.removeEventListener('click', hideOnClick);
            }
        };

        setTimeout(() => {
            document.addEventListener('click', hideOnClick);
        }, 100);
    }

    // 隐藏右键菜单
    private hideContextMenu() {
        const contextMenu = document.getElementById('context-menu');
        if (contextMenu) {
            document.body.removeChild(contextMenu);
        }
    }

    // 处理选择文本的操作
    private handleSelectionAction(action: string, selectedText: string) {
        let prompt = '';

        switch (action) {
            case 'ask-ai':
                // 对于直接询问，打开AI面板并预填充文本，让用户可以编辑问题
                this.openAIWithPrefilledText(selectedText);
                return;
            case 'explain':
                prompt = `请解释以下内容：\n\n${selectedText}`;
                break;
            case 'summarize':
                prompt = `请总结以下内容：\n\n${selectedText}`;
                break;
            case 'translate':
                prompt = `请将以下内容翻译成中文（如果是中文则翻译成英文）：\n\n${selectedText}`;
                break;
            default:
                return;
        }

        // 确保AI对话面板可见
        if (!this.state.aiChatVisible) {
            this.setState(() => ({ aiChatVisible: true }));
        }

        // 发送消息给AI
        this.sendMessageToAI(prompt);

        // 显示提示消息
        const actionNames: { [key: string]: string } = {
            'ask-ai': '询问AI',
            'explain': '解释内容',
            'summarize': '总结内容',
            'translate': '翻译内容'
        };

        this.showTemporaryMessage(`正在${actionNames[action]}...`);
    }

    // 打开AI面板并预填充文本
    private openAIWithPrefilledText(text: string) {
        // 确保AI对话面板可见
        if (!this.state.aiChatVisible) {
            this.setState(() => ({ aiChatVisible: true }));
        }

        // 延迟设置输入框内容，确保面板已经渲染
        setTimeout(() => {
            const aiInput = this.root.querySelector('#ai-input') as HTMLTextAreaElement;
            if (aiInput) {
                aiInput.value = text;
                aiInput.focus();
                // 将光标移到文本末尾
                aiInput.setSelectionRange(text.length, text.length);
            }
        }, 100);

        this.showTemporaryMessage('已将选中内容填入AI输入框');
    }

    // --- 密码管理相关方法 ---

    // 显示密码修改对话框
    private showPasswordChangeDialog = () => {
        this.setState(() => ({
            passwordChangeVisible: true,
            errorMessage: null
        }));
    }

    // 隐藏密码修改对话框
    private hidePasswordChangeDialog = () => {
        this.setState(() => ({
            passwordChangeVisible: false,
            passwordChangeLoading: false,
            errorMessage: null
        }));

        // 清空表单
        const form = this.root.querySelector('#password-change-form') as HTMLFormElement;
        if (form) {
            form.reset();
        }
    }

    // 处理密码修改
    private handlePasswordChange = async () => {
        const currentPasswordInput = this.root.querySelector<HTMLInputElement>('#current-password');
        const newPasswordInput = this.root.querySelector<HTMLInputElement>('#new-password');
        const confirmPasswordInput = this.root.querySelector<HTMLInputElement>('#confirm-password');

        if (!currentPasswordInput || !newPasswordInput || !confirmPasswordInput) {
            return;
        }

        const currentPassword = currentPasswordInput.value;
        const newPassword = newPasswordInput.value;
        const confirmPassword = confirmPasswordInput.value;

        // 基本输入验证
        if (!currentPassword) {
            this.setState(() => ({
                errorMessage: '请输入当前密码'
            }));
            currentPasswordInput.focus();
            return;
        }

        if (!newPassword) {
            this.setState(() => ({
                errorMessage: '请输入新密码'
            }));
            newPasswordInput.focus();
            return;
        }

        if (!confirmPassword) {
            this.setState(() => ({
                errorMessage: '请确认新密码'
            }));
            confirmPasswordInput.focus();
            return;
        }

        // 第一步：验证当前密码是否正确
        try {
            this.setState(() => ({
                passwordChangeLoading: true,
                errorMessage: null
            }));

            // 先验证当前密码
            await this.verifyCurrentPassword(currentPassword);

        } catch (error) {
            console.error('当前密码验证失败:', error);
            const errorMessage = error instanceof Error ? error.message : '当前密码验证失败';
            this.setState(() => ({
                passwordChangeLoading: false,
                errorMessage: errorMessage
            }));
            // 聚焦到当前密码输入框
            currentPasswordInput.focus();
            currentPasswordInput.select();
            return;
        }

        // 第二步：验证新密码格式
        if (newPassword.length < 6) {
            this.setState(() => ({
                passwordChangeLoading: false,
                errorMessage: '新密码长度至少为6位'
            }));
            newPasswordInput.focus();
            newPasswordInput.select();
            return;
        }

        if (newPassword !== confirmPassword) {
            this.setState(() => ({
                passwordChangeLoading: false,
                errorMessage: '新密码和确认密码不匹配'
            }));
            confirmPasswordInput.focus();
            confirmPasswordInput.select();
            return;
        }

        if (currentPassword === newPassword) {
            this.setState(() => ({
                passwordChangeLoading: false,
                errorMessage: '新密码不能与当前密码相同'
            }));
            newPasswordInput.focus();
            newPasswordInput.select();
            return;
        }

        // 第三步：执行密码修改
        try {
            await api.changePassword(currentPassword, newPassword);

            // 修改成功
            this.showTemporaryMessage('密码修改成功');
            this.hidePasswordChangeDialog();

        } catch (error) {
            console.error('密码修改错误:', error);
            const errorMessage = error instanceof Error ? error.message : '密码修改失败';
            this.setState(() => ({
                passwordChangeLoading: false,
                errorMessage: errorMessage
            }));
        }
    }

    // 验证当前密码是否正确
    private verifyCurrentPassword = async (currentPassword: string): Promise<void> => {
        // 使用一个临时的密码修改请求来验证当前密码
        // 我们传入一个临时的新密码，如果当前密码错误，后端会立即返回错误
        try {
            const response = await fetch(`${api.getBaseUrl()}/auth/verify-current-password`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${api.getToken()}`
                },
                body: JSON.stringify({ currentPassword })
            });

            if (!response.ok) {
                let errorMessage = '当前密码错误';
                try {
                    const error = await response.json();
                    errorMessage = error.message || errorMessage;
                } catch (e) {
                    errorMessage = response.statusText || errorMessage;
                }
                throw new Error(errorMessage);
            }
        } catch (error) {
            if (error instanceof TypeError && error.message.includes('fetch')) {
                throw new Error('网络连接失败，请检查网络或服务器状态');
            }
            throw error;
        }
    }

    // 渲染密码修改对话框
    private renderPasswordChangeDialog(): string {
        if (!this.state.passwordChangeVisible) {
            return '';
        }

        const errorDisplay = this.state.errorMessage ? 'block' : 'none';
        const errorText = this.state.errorMessage || '';
        const isLoading = this.state.passwordChangeLoading;

        return `
            <div class="modal-overlay" id="password-change-overlay">
                <div class="modal-dialog">
                    <div class="modal-header">
                        <h3>修改密码</h3>
                        <button class="modal-close-btn" id="password-change-close">
                            <span class="material-symbols-outlined">close</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="password-change-error" style="display: ${errorDisplay};">${errorText}</div>
                        <form id="password-change-form">
                            <div class="form-group">
                                <label for="current-password">当前密码</label>
                                <input type="password" id="current-password" placeholder="请输入当前密码" ${isLoading ? 'disabled' : ''}>
                            </div>
                            <div class="form-group">
                                <label for="new-password">新密码</label>
                                <input type="password" id="new-password" placeholder="请输入新密码（至少6位）" ${isLoading ? 'disabled' : ''}>
                            </div>
                            <div class="form-group">
                                <label for="confirm-password">确认新密码</label>
                                <input type="password" id="confirm-password" placeholder="请再次输入新密码" ${isLoading ? 'disabled' : ''}>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" id="password-change-cancel" ${isLoading ? 'disabled' : ''}>取消</button>
                        <button class="btn btn-primary" id="password-change-submit" ${isLoading ? 'disabled' : ''}>
                            ${isLoading ? '修改中...' : '确认修改'}
                        </button>
                    </div>
                </div>
            </div>
        `;
    }
}

document.addEventListener('DOMContentLoaded', () => {
    const rootElement = document.getElementById('root');
    if (rootElement) {
        // 添加现代化登录表单的CSS样式
        const loginStyles = document.createElement('style');
        loginStyles.textContent = `
            .login-container {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100vh;
                background: #f8fafc;
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .login-form {
                background: white;
                padding: 40px;
                border-radius: 12px;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
                width: 400px;
                max-width: 90vw;
                border: 1px solid #e5e7eb;
            }

            .login-form h2 {
                margin: 0 0 24px 0;
                color: #1f2937;
                text-align: center;
                font-size: 24px;
                font-weight: 600;
            }

            .form-group {
                margin-bottom: 20px;
            }

            .form-group label {
                display: block;
                margin-bottom: 6px;
                color: #374151;
                font-weight: 500;
                font-size: 14px;
            }

            .form-group input {
                width: 100%;
                padding: 12px 16px;
                border: 1px solid #d1d5db;
                border-radius: 8px;
                font-size: 16px;
                box-sizing: border-box;
                transition: border-color 0.2s, box-shadow 0.2s;
                background: white;
                color: #1f2937;
            }

            .form-group input:focus {
                border-color: #3b82f6;
                outline: none;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            .form-group input::placeholder {
                color: #9ca3af;
            }

            .login-btn {
                width: 100%;
                padding: 12px 24px;
                background: #3b82f6;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 500;
                cursor: pointer;
                transition: background-color 0.2s;
                margin-top: 8px;
            }

            .login-btn:hover {
                background: #2563eb;
            }

            .login-btn:disabled {
                background: #9ca3af;
                cursor: not-allowed;
            }

            .login-error {
                background: rgba(239, 68, 68, 0.1);
                color: #dc2626;
                padding: 16px 20px;
                border-radius: 12px;
                margin-bottom: 20px;
                font-size: 14px;
                font-weight: 500;
                text-align: center;
                border: 1px solid rgba(239, 68, 68, 0.2);
                backdrop-filter: blur(10px);
                animation: shake 0.5s ease-in-out;
            }

            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-8px); }
                75% { transform: translateX(8px); }
            }
            
            .login-info {
                margin-top: 20px;
                padding-top: 16px;
                border-top: 1px solid #eee;
                font-size: 14px;
                color: #666;
            }
            
            .login-info p {
                margin: 5px 0;
            }
            
            .logout-btn {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 4px 8px;
                font-size: 12px;
                cursor: pointer;
                margin-top: 5px;
                display: block;
            }
            
            .logout-btn:hover {
                background-color: #d32f2f;
            }
            
            /* 简洁加载状态样式 */
            .loading-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(255, 255, 255, 0.9);
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            }

            .loading-spinner {
                width: 40px;
                height: 40px;
                border: 3px solid #e5e7eb;
                border-top: 3px solid #3b82f6;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            .loading-text {
                margin-top: 16px;
                font-size: 16px;
                font-weight: 500;
                color: #374151;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            /* 错误信息样式 */
            .error-message {
                position: fixed;
                top: 20px;
                right: 20px;
                background-color: #f44336;
                color: white;
                padding: 12px 16px;
                border-radius: 4px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
                display: flex;
                align-items: center;
                z-index: 1001;
                animation: slideIn 0.3s ease-out;
            }
            
            .error-message span {
                margin-right: 8px;
            }
            
            .close-error-btn {
                background: none;
                border: none;
                color: white;
                font-size: 20px;
                cursor: pointer;
                margin-left: 16px;
                padding: 0;
            }
            
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            /* 密码修改对话框样式 */
            .modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                animation: fadeIn 0.3s ease;
            }

            .modal-dialog {
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                width: 400px;
                max-width: 90vw;
                animation: slideUp 0.3s ease;
            }

            .modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 20px 24px 16px;
                border-bottom: 1px solid #e0e0e0;
            }

            .modal-header h3 {
                margin: 0;
                font-size: 18px;
                font-weight: 600;
                color: #333;
            }

            .modal-close-btn {
                background: none;
                border: none;
                cursor: pointer;
                padding: 4px;
                border-radius: 4px;
                color: #666;
                transition: background-color 0.2s;
            }

            .modal-close-btn:hover {
                background-color: #f5f5f5;
            }

            .modal-body {
                padding: 24px;
            }

            .modal-footer {
                display: flex;
                justify-content: flex-end;
                gap: 12px;
                padding: 16px 24px 24px;
                border-top: 1px solid #e0e0e0;
            }

            .password-change-error {
                background-color: #ffebee;
                color: #d32f2f;
                padding: 12px;
                border-radius: 4px;
                margin-bottom: 16px;
                font-size: 14px;
                text-align: center;
                border: 1px solid #ffcdd2;
                animation: shake 0.5s ease-in-out;
            }

            .btn {
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: background-color 0.2s;
            }

            .btn:disabled {
                opacity: 0.6;
                cursor: not-allowed;
            }

            .btn-primary {
                background-color: #4a90e2;
                color: white;
            }

            .btn-primary:hover:not(:disabled) {
                background-color: #3a80d2;
            }

            .btn-secondary {
                background-color: #f5f5f5;
                color: #666;
            }

            .btn-secondary:hover:not(:disabled) {
                background-color: #e0e0e0;
            }

            .settings-btn {
                background: none;
                border: none;
                cursor: pointer;
                padding: 6px;
                border-radius: 4px;
                color: #666;
                transition: background-color 0.2s;
                margin-right: 8px;
            }

            .settings-btn:hover {
                background-color: rgba(255, 255, 255, 0.1);
            }

            .profile-actions {
                display: flex;
                align-items: center;
                margin-top: 8px;
            }

            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }

            @keyframes slideUp {
                from { transform: translateY(20px); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }

            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }

            /* 分析结果样式 */
            .analysis-card {
                background: white;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 20px;
                margin-bottom: 16px;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            }

            .analysis-card-title {
                font-weight: 600;
                color: #1f2937;
                margin: 0 0 12px 0;
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 16px;
            }

            .analysis-card-title .material-symbols-outlined {
                font-size: 20px;
                color: #4f46e5;
            }

            .keyword-tag, .topic-tag {
                display: inline-block;
                font-weight: 500;
                border: 1px solid transparent;
                transition: all 0.2s;
            }

            .keyword-tag:hover {
                background: #dbeafe !important;
                border-color: #3b82f6;
            }

            .topic-tag:hover {
                background: #dcfce7 !important;
                border-color: #22c55e;
            }

            .search-type-btn.loading {
                opacity: 0.7;
                cursor: not-allowed;
            }

            .search-type-btn:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }
        `;
        document.head.appendChild(loginStyles);

        const app = new NotesApp(rootElement);
        // 暴露到全局作用域以便HTML中的onclick可以访问
        (window as any).app = app;
    }
});