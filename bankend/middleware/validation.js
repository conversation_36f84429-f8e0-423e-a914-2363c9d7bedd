const validator = require('validator');
const DOMPurify = require('isomorphic-dompurify');

// 通用输入验证函数
const validateInput = {
  // 验证字符串长度和内容
  string: (value, minLength = 0, maxLength = 1000, allowEmpty = false) => {
    if (!allowEmpty && (!value || typeof value !== 'string' || value.trim().length === 0)) {
      return { valid: false, message: '输入不能为空' };
    }
    
    if (value && typeof value === 'string') {
      const trimmed = value.trim();
      if (trimmed.length < minLength) {
        return { valid: false, message: `输入长度至少为${minLength}个字符` };
      }
      if (trimmed.length > maxLength) {
        return { valid: false, message: `输入长度不能超过${maxLength}个字符` };
      }
    }
    
    return { valid: true, sanitized: value ? value.trim() : '' };
  },

  // 验证HTML内容（用于笔记内容）
  html: (value, maxLength = 50000) => {
    if (!value || typeof value !== 'string') {
      return { valid: true, sanitized: '' };
    }

    if (value.length > maxLength) {
      return { valid: false, message: `内容长度不能超过${maxLength}个字符` };
    }

    // 使用DOMPurify清理HTML，防止XSS
    const sanitized = DOMPurify.sanitize(value, {
      ALLOWED_TAGS: [
        'p', 'br', 'div', 'span', 'strong', 'b', 'em', 'i', 'u', 's', 'strike',
        'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
        'ul', 'ol', 'li',
        'a', 'img',
        'table', 'thead', 'tbody', 'tr', 'th', 'td',
        'blockquote', 'code', 'pre'
      ],
      ALLOWED_ATTR: [
        'href', 'target', 'rel', 'src', 'alt', 'title',
        'style', 'class', 'id',
        'colspan', 'rowspan'
      ],
      ALLOWED_URI_REGEXP: /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp|data):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i
    });

    return { valid: true, sanitized };
  },

  // 验证数字ID
  id: (value) => {
    const id = parseInt(value);
    if (isNaN(id) || id <= 0) {
      return { valid: false, message: '无效的ID' };
    }
    return { valid: true, sanitized: id };
  },

  // 验证文件夹名称
  folderName: (value) => {
    const result = validateInput.string(value, 1, 100);
    if (!result.valid) return result;

    // 检查是否包含非法字符
    if (!/^[a-zA-Z0-9\u4e00-\u9fa5\s\-_()（）]+$/.test(result.sanitized)) {
      return { valid: false, message: '文件夹名称包含非法字符' };
    }

    return result;
  },

  // 验证笔记标题
  noteTitle: (value) => {
    const result = validateInput.string(value, 0, 200, true);
    if (!result.valid) return result;

    // 如果为空，使用默认标题
    if (!result.sanitized) {
      return { valid: true, sanitized: '无标题笔记' };
    }

    return result;
  }
};

// 中间件：验证笔记创建/更新
const validateNote = (req, res, next) => {
  const { title, content, folderId } = req.body;

  // 验证标题（只有在提供了标题时才验证和设置）
  if (title !== undefined) {
    const titleValidation = validateInput.noteTitle(title);
    if (!titleValidation.valid) {
      return res.status(400).json({ message: titleValidation.message });
    }
    req.body.title = titleValidation.sanitized;
  }

  // 验证内容（只有在提供了内容时才验证和设置）
  if (content !== undefined) {
    const contentValidation = validateInput.html(content);
    if (!contentValidation.valid) {
      return res.status(400).json({ message: contentValidation.message });
    }
    req.body.content = contentValidation.sanitized;
  }

  // 验证文件夹ID（如果提供）
  if (folderId !== undefined && folderId !== null) {
    const folderIdValidation = validateInput.id(folderId);
    if (!folderIdValidation.valid) {
      return res.status(400).json({ message: '无效的文件夹ID' });
    }
    req.body.folderId = folderIdValidation.sanitized;
  }

  next();
};

// 中间件：验证文件夹创建/更新
const validateFolder = (req, res, next) => {
  const { name, parentId } = req.body;

  // 验证文件夹名称
  const nameValidation = validateInput.folderName(name);
  if (!nameValidation.valid) {
    return res.status(400).json({ message: nameValidation.message });
  }

  // 验证父文件夹ID（如果提供）
  if (parentId !== undefined && parentId !== null) {
    const parentIdValidation = validateInput.id(parentId);
    if (!parentIdValidation.valid) {
      return res.status(400).json({ message: '无效的父文件夹ID' });
    }
    req.body.parentId = parentIdValidation.sanitized;
  }

  // 设置清理后的数据
  req.body.name = nameValidation.sanitized;

  next();
};

module.exports = {
  validateInput,
  validateNote,
  validateFolder
};
